import { defineConfig, loadEnv, UserConfigFnObject } from "vite"
import react from "@vitejs/plugin-react-swc"
import generateSitemap from "vite-ssg-sitemap"
import viteCompression from 'vite-plugin-compression';
import { VitePWA } from 'vite-plugin-pwa'
// import { visualizer } from 'rollup-plugin-visualizer'
import { parseStringPromise, Builder } from "xml2js"
import path from "path"
import fs from "fs/promises"
import i18n from './src/locales'

const paths: string[] = []
const siteMapExclude: string[] = []

// https://vitejs.dev/config/
const appConfig: UserConfigFnObject = ({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  build: {
    // 由于部署需要更改outDir目录 dist将作为服务器目录
    outDir: 'static',
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (/[\/\\]node_modules[\/\\]@radix-ui[\/\\]/.test(id)) {
            return 'radix-ui';
          }
          if (/[\/\\]node_modules[\/\\]@remix-run[\/\\]/.test(id)) {
            return 'remix-run';
          }
          if (/[\/\\]node_modules[\/\\]react[\/\\]/.test(id) || /[\/\\]node_modules[\/\\]react-dom[\/\\]/.test(id) || /[\/\\]node_modules[\/\\]react-router[\/\\]/.test(id) || /[\/\\]node_modules[\/\\]react-router-dom[\/\\]/.test(id)) {
            return 'react';
          }
        },
      },
    },
  },
  plugins: [
    react(),
    // visualizer({
    //   open: true, // 构建后自动打开分析页面
    //     gzipSize: true,
    //     brotliSize: true
    // }),
    // gzip 压缩配置
    viteCompression({
      algorithm: 'gzip',           // 压缩算法
      ext: '.gz',                  // 文件扩展名
      threshold: 10240,            // 只压缩大于 10kb 的文件
      deleteOriginFile: false,     // 保留原始文件
      filter: /\.(js|css|svg)$/, // 只压缩这些类型的文件
      compressionOptions: {
        level: 9,                  // 压缩级别 (1-9, 9为最高压缩比)
      },
      verbose: true,               // 显示压缩信息
    }),
    // PWA
    VitePWA({
      registerType: 'autoUpdate', // 自动更新SW
      manifest: {
        name: 'Miuta',
        short_name: 'Miuta',
        start_url: '.',
        display: 'standalone',
        background_color: '#ffffff',
        theme_color: '#42b983',
        icons: [
          {
            src: `icon-192x192.webp?v=${loadEnv(mode, process.cwd(), '').VITE_APP_HASH}`,
            sizes: '192x192',
            type: 'image/webp'
          },
          {
            src: `icon-512x512.webp?v=${loadEnv(mode, process.cwd(), '').VITE_APP_HASH}`,
            sizes: '512x512',
            type: 'image/webp'
          }
        ],
      },
      workbox: {
        // 不预缓存任何文件
        globPatterns: [],
        // 不做任何运行时缓存
        runtimeCaching: []
      },
      // 也可以用 injectRegister: 'auto'，更简单
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  ssgOptions: {
    includedRoutes(p: string[]) {
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      p.forEach(path => {
        languages.forEach(lang => {
          if (path.endsWith(`/${lang}/`)) {
            siteMapExclude.push(path)
          }
        })
      });
      // 记录路径
      paths.push(...p)
      return p
    },
    async onBeforePageRender(route: string) {
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      const defaultLanguage = loadEnv(mode, process.cwd(), '').VITE_APP_I18N_DEFAULT
      const dirs = route.split(path.sep)
      const lang = languages.includes(dirs[dirs.length - 2]) ? dirs[dirs.length - 2] : defaultLanguage
      // 对应public/locales配置 约定首页是translation 其他页面根据路由根目录命名 如/terms/ 对应public/locales/xxx/terms.json
      const ns = (route === '/' || route === `/${lang}/`) ? 'translation' : dirs[1]
      const json = await fs.readFile(`public/locales/${lang}/${ns}.json`, 'utf-8')
      const translation = JSON.parse(json)
      i18n.changeLanguage(lang)
      i18n.addResourceBundle(lang, 'translation', translation);
    },
    async onFinished() {
      console.log(`开始处理html语言...`)
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      const defaultLanguage = loadEnv(mode, process.cwd(), '').VITE_APP_I18N_DEFAULT
      const promise = paths.map(async p => {
        // 特殊路径不处理
        if (p === '*') {
          return
        }
        if (p.endsWith(':lang?')) {
          return
        }
        const dirs = p.split(path.sep)
        const lang = languages.includes(dirs[dirs.length - 2]) ? dirs[dirs.length - 2] : defaultLanguage
        // p 参考值："/" "/blog/"
        const html = await fs.readFile(`static${p}index.html`, 'utf-8')
        const i18nHTML = html.replace(/<html.*?>/, `<html lang="${lang}">`)
        await fs.writeFile(`static${p}index.html`, i18nHTML, 'utf-8')
        console.log(`html语言 已处理: ${p}，lang=${lang}`)
      })
      await Promise.all(promise)
      console.log(`sitemap.xml生成`)
      generateSitemap({
        outDir: 'static',
        hostname: 'https://miuta.ai/',
        // 若要做多语言路由 sitemap需要特殊处理 这里要过滤掉非默认语言的路由处理 然后让sitemap自己去生成多语言路由的sitemap.xml内容 否则生成内容不符合要求
        i18n: {
          strategy: 'suffix',
          defaultLanguage: 'en',
          languages: languages.filter(lang => lang !== 'en'),
        }
      })
      // 排除sitemap.xml中不需要的多语言路径
      const MAX_WAIT_SECONDS = 30 // 最长等待时间（秒）
      const CHECK_INTERVAL_MS = 500 // 触发间隔
      const sitemapPath = path.resolve(__dirname, 'static', 'sitemap.xml')
      const maxAttempts = (MAX_WAIT_SECONDS * 1000) / CHECK_INTERVAL_MS

      let intervalId: NodeJS.Timeout | null = null
      let attempts = 0

      console.log(`开始轮询检查 sitemap.xml 文件是否存在... (最长等待 ${MAX_WAIT_SECONDS} 秒)`)

      intervalId = setInterval(async () => {
        attempts++
        try {
          // 尝试访问文件，如果不存在会抛出错误
          await fs.access(sitemapPath)
          // 文件存在！
          if (intervalId) {
            clearInterval(intervalId)
          }
          console.log(`文件 sitemap.xml 已找到！开始执行处理代码...`)
          try {
            // 1. 读取和解析 XML
            const xmlContent = await fs.readFile(sitemapPath, 'utf-8')
            const parsedXml = await parseStringPromise(xmlContent)

            if (!parsedXml.urlset || !parsedXml.urlset.url) {
              console.log('Sitemap 文件格式不正确，跳过处理。')
              return
            }

            // 排除逻辑
            const shouldExcludeUrl = (url: string): boolean => {
              // 这里不需要判断尾斜杠
              return siteMapExclude.some(exclude => url.endsWith(exclude.slice(0, -1)))
            };
      
            // 过滤并重建
            const urlsToKeep = parsedXml.urlset.url.filter((u: any) => !shouldExcludeUrl(u.loc[0]))
            // 给所有路径统一加上尾斜杠/
            urlsToKeep.forEach((u: any) => {
              if (!u.loc[0].endsWith('/')) {
                u.loc[0] = u.loc[0] + '/'
              }
              u['xhtml:link'].forEach((link: any) => {
                if (!link.$.href.endsWith('/')) {
                  link.$.href = link.$.href + '/'
                }
              })
            })
            const removedCount = parsedXml.urlset.url.length - urlsToKeep.length
      
            parsedXml.urlset.url = urlsToKeep
            const builder = new Builder()
            const newXmlContent = builder.buildObject(parsedXml)
            await fs.writeFile(sitemapPath, newXmlContent)
            console.log(`成功从 sitemap.xml 中移除了 ${removedCount} 个 URL。`)
          } catch (e: any) {
            console.error(`处理 sitemap.xml 时出错: ${e.message}`)
          }
        } catch (error) {
          // 文件不存在，继续轮询
          if (attempts > maxAttempts) {
            if (intervalId) {
              clearInterval(intervalId)
            }
            console.error(`等待超时，未找到 sitemap.xml 文件。`)
          }
          // 如果是其他错误，也停止并报告
          else if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
             if (intervalId) {
              clearInterval(intervalId)
             }
             console.error(`检查文件时发生意外错误: ${(error as Error).message}`)
          }
        }
      }, CHECK_INTERVAL_MS);
    }
  },
});

const libConfig: UserConfigFnObject = () => ({
plugins: [react()],
  build: {
    copyPublicDir: false,
    outDir: 'lib', // 可自定义
    lib: {
      entry: {
        blog: path.resolve(__dirname, 'src/server/Blog.tsx'),
      },
      formats: ['es'],
      fileName: (format, entryName) => `${entryName}.${format}.js`
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@app": path.resolve(__dirname, "./src/core")
    },
  },
}); 

// https://vitejs.dev/config/
export default defineConfig((config) => {
  if (config.mode === 'lib') {
    return libConfig(config)
  }
  return appConfig(config)
});
