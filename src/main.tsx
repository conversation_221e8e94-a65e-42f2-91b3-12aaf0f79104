// https://vite-react-ssg.netlify.app/docs/getting-started
import { ViteReactSSG } from "vite-react-ssg";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { default as LanguageSync } from "@/components/LanguageSync";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import Terms from "@/pages/Terms";
import Privacy from "@/pages/Privacy";
import Games from "@/pages/use-cases/Games";
import Startups from "@/pages/use-cases/Startups";
import MarketingAgencies from "@/pages/use-cases/MarketingAgencies";
import HealthFitness from "@/pages/use-cases/HealthFitness";
import Advertising from "@/pages/use-cases/Advertising";
import AffiliateMarketing from "@/pages/use-cases/AffiliateMarketing";
import Ecommerce from "@/pages/use-cases/Ecommerce";
import DTCBrands from "@/pages/use-cases/DTCBrands";
import Tiktok from "@/pages/use-cases/Tiktok";
import AIAvatars from "@/pages/features/AIAvatars";
import AIScriptGenerator from "@/pages/features/AIScriptGenerator";
import AIInstagramAds from "@/pages/features/AIInstagramAds";
import AIFacebookAds from "@/pages/features/AIFacebookAds";
import AITikTokAds from "@/pages/features/AITikTokAds";
import AIYouTubeAds from "@/pages/features/AIYouTubeAds";
import AILogoGenerator from "@/pages/features/AILogoGenerator";
import FacelessContentCreator from "@/pages/features/FacelessContentCreator";
import AIVideoEditor from "@/pages/features/AIVideoEditor";
import FreeAITools from "@/pages/features/FreeAITools";
import { initI18n } from "./locales";
import "./index.css";

const queryClient = new QueryClient();

const Layout = ({ children }: { children?: React.ReactNode }) => (
  <LanguageSync>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        {children}
      </TooltipProvider>
    </QueryClientProvider>
  </LanguageSync>
);

export const routes = [
  {
    path: "/:lang?",
    element: (
      <Layout>
        <Index />
      </Layout>
    ),
    title: "Index",
    getStaticPaths: () =>
      import.meta.env.VITE_APP_I18N.split(",").map((lang: string) =>
        lang !== import.meta.env.VITE_APP_I18N_DEFAULT ? `/${lang}/` : "/"
      ),
  },
  {
    path: "/terms/",
    element: (
      <Layout>
        <Terms />
      </Layout>
    ),
  },
  {
    path: "/privacy/",
    element: (
      <Layout>
        <Privacy />
      </Layout>
    ),
  },
  // Use Cases Routes
  {
    path: "/use-cases/games/",
    element: (
      <Layout>
        <Games />
      </Layout>
    ),
  },
  {
    path: "/use-cases/startups/",
    element: (
      <Layout>
        <Startups />
      </Layout>
    ),
  },
  {
    path: "/use-cases/marketing-agencies/",
    element: (
      <Layout>
        <MarketingAgencies />
      </Layout>
    ),
  },
  {
    path: "/use-cases/health-fitness/",
    element: (
      <Layout>
        <HealthFitness />
      </Layout>
    ),
  },
  {
    path: "/use-cases/advertising/",
    element: (
      <Layout>
        <Advertising />
      </Layout>
    ),
  },
  {
    path: "/use-cases/affiliate-marketing/",
    element: (
      <Layout>
        <AffiliateMarketing />
      </Layout>
    ),
  },
  {
    path: "/use-cases/ecommerce/",
    element: (
      <Layout>
        <Ecommerce />
      </Layout>
    ),
  },
  {
    path: "/use-cases/dtc-brands/",
    element: (
      <Layout>
        <DTCBrands />
      </Layout>
    ),
  },
  {
    path: "/use-cases/tiktok/",
    element: (
      <Layout>
        <Tiktok />
      </Layout>
    ),
  },
  // Features Routes
  {
    path: "/features/ai-avatars/",
    element: (
      <Layout>
        <AIAvatars />
      </Layout>
    ),
  },
  {
    path: "/features/ai-script-generator/",
    element: (
      <Layout>
        <AIScriptGenerator />
      </Layout>
    ),
  },
  {
    path: "/features/ai-instagram-ads/",
    element: (
      <Layout>
        <AIInstagramAds />
      </Layout>
    ),
  },
  {
    path: "/features/ai-facebook-ads/",
    element: (
      <Layout>
        <AIFacebookAds />
      </Layout>
    ),
  },
  {
    path: "/features/ai-tiktok-ads/",
    element: (
      <Layout>
        <AITikTokAds />
      </Layout>
    ),
  },
  {
    path: "/features/ai-youtube-ads/",
    element: (
      <Layout>
        <AIYouTubeAds />
      </Layout>
    ),
  },
  {
    path: "/features/ai-logo-generator/",
    element: (
      <Layout>
        <AILogoGenerator />
      </Layout>
    ),
  },
  {
    path: "/features/faceless-content-creator/",
    element: (
      <Layout>
        <FacelessContentCreator />
      </Layout>
    ),
  },
  {
    path: "/features/ai-video-editor/",
    element: (
      <Layout>
        <AIVideoEditor />
      </Layout>
    ),
  },
  {
    path: "/features/free-ai-tools/",
    element: (
      <Layout>
        <FreeAITools />
      </Layout>
    ),
  },
  {
    path: "*",
    element: (
      <Layout>
        <NotFound />
      </Layout>
    ),
    title: "Not Found",
  },
];

export const createRoot = ViteReactSSG(
  {
    routes,
  },
  async () => {
    await initI18n();
  }
);
