import { Button } from "@/components/ui/button";
import { Play } from "lucide-react";

interface FeatureHeroProps {
  title: string;
  subtitle: string;
  description: string;
  ctaText?: string;
  demoText?: string;
}

export function FeatureHero({ title, subtitle, description, ctaText = "Start Creating", demoText = "Book a Demo" }: FeatureHeroProps) {
  return (
    <section className="relative overflow-hidden py-24 lg:py-32">
      <div className="container mx-auto px-6">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="gradient-text">{title}</span>
          </h1>
          
          <p className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white mb-8">
            {subtitle}
          </p>
          
          <p className="text-lg md:text-xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-primary to-secondary hover:shadow-glow text-white px-8 py-4 text-lg font-semibold"
            >
              <Play className="w-5 h-5 mr-2" />
              {ctaText}
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg"
            >
              {demoText}
            </Button>
          </div>
        </div>
        
        <div className="mt-16 text-center">
          <p className="text-white/60 text-sm mb-8">
            Trusted by individuals and teams at the world's boldest companies
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {/* Company logos placeholder */}
            <div className="w-24 h-8 bg-white/10 rounded-md flex items-center justify-center">
              <span className="text-white/40 text-xs">Company</span>
            </div>
            <div className="w-24 h-8 bg-white/10 rounded-md flex items-center justify-center">
              <span className="text-white/40 text-xs">Company</span>
            </div>
            <div className="w-24 h-8 bg-white/10 rounded-md flex items-center justify-center">
              <span className="text-white/40 text-xs">Company</span>
            </div>
            <div className="w-24 h-8 bg-white/10 rounded-md flex items-center justify-center">
              <span className="text-white/40 text-xs">Company</span>
            </div>
            <div className="w-24 h-8 bg-white/10 rounded-md flex items-center justify-center">
              <span className="text-white/40 text-xs">Company</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
