import { Card } from "@/components/ui/card";
import { 
  User, 
  Mic, 
  Video, 
  Globe, 
  Zap, 
  Target,
  Users,
  TrendingUp,
  Clock
} from "lucide-react";
import { useTranslation } from 'react-i18next';

export function Features() {
  const { t } = useTranslation();

  const features = [
    {
      icon: User,
      title: t('features.aiActorsTitle'),
      description: t('features.aiActorsDescription')
    },
    {
      icon: Mic,
      title: t('features.aiVoiceoverTitle'),
      description: t('features.aiVoiceoverDescription')
    },
    {
      icon: Video,
      title: t('features.aiUgcVideoAdsTitle'),
      description: t('features.aiUgcVideoAdsDescription')
    },
    {
      icon: Globe,
      title: t('features.urlToVideoTitle'),
      description: t('features.urlToVideoDescription')
    },
    {
      icon: Zap,
      title: t('features.lightningFastTitle'),
      description: t('features.lightningFastDescription')
    },
    {
      icon: Target,
      title: t('features.brandTargetingTitle'),
      description: t('features.brandTargetingDescription')
    },
    {
      icon: Users,
      title: t('features.multiPlatformTitle'),
      description: t('features.multiPlatformDescription')
    },
    {
      icon: TrendingUp,
      title: t('features.viralOptimizationTitle'),
      description: t('features.viralOptimizationDescription')
    },
    {
      icon: Clock,
      title: t('features.production247Title'),
      description: t('features.production247Description')
    }
  ];

  return (
    <section id="features" className="py-24 relative">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('features.title')}
            <span className="text-gradient"> {t('features.highlight')}</span>
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const featureLinks = {
              [t('features.aiActorsTitle')]: "/features/ai-avatars",
              [t('features.aiVoiceoverTitle')]: "/features/ai-script-generator", 
              [t('features.aiUgcVideoAdsTitle')]: "/features/ai-instagram-ads",
              [t('features.urlToVideoTitle')]: "/features/ai-script-generator",
              [t('features.multiPlatformTitle')]: "/features/ai-tiktok-ads",
              [t('features.viralOptimizationTitle')]: "/use-cases/tiktok"
            };
            
            const CardComponent = featureLinks[feature.title] ? 'a' : 'div';
            
            return (
              <Card 
                key={index} 
                className="gradient-card border-white/10 p-8 hover:shadow-card transition-all duration-300 hover:scale-105"
              >
                <CardComponent 
                  {...(featureLinks[feature.title] && { href: featureLinks[feature.title] })}
                  className={featureLinks[feature.title] ? "block group" : "block"}
                >
                  <div className="w-12 h-12 rounded-lg gradient-button flex items-center justify-center mb-6">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className={`text-xl font-semibold text-white mb-4 ${featureLinks[feature.title] ? 'group-hover:text-primary transition-colors' : ''}`}>
                    {feature.title}
                  </h3>
                  <p className="text-white/70 leading-relaxed">
                    {feature.description}
                  </p>
                </CardComponent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
