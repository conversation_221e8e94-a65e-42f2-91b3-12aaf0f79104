import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQ {
  question: string;
  answer: string;
}

interface FeatureFAQProps {
  title?: string;
  faqs: FAQ[];
}

export function FeatureFAQ({ title = "Frequently Asked Questions", faqs }: FeatureFAQProps) {
  return (
    <section className="py-20 lg:py-28">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-16">
            <span className="gradient-text">{title}</span>
          </h2>
          
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem 
                key={index} 
                value={`item-${index}`}
                className="border border-white/10 rounded-xl px-6 bg-gradient-card"
              >
                <AccordionTrigger className="text-white hover:text-primary text-left py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-white/80 pb-6">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
