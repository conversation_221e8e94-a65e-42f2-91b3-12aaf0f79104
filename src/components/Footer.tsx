import { Play } from "lucide-react";
import { useTranslation } from 'react-i18next';

export function Footer() {
  const { t } = useTranslation();

  return (
    <footer className="py-16 border-t border-white/10">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 rounded-lg gradient-button flex items-center justify-center">
                <Play className="w-4 h-4 text-white" />
              </div>
              <span className="text-xl font-bold text-white">Miuta</span>
            </div>
            <p className="text-white/60 leading-relaxed">
              {t('footer.description')}
            </p>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">{t('footer.sections.product.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.product.links.features')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.product.links.pricing')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.product.links.api')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.product.links.integrations')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.product.links.changelog')}
                </a>
              </li>
            </ul>
          </div>

          {/* Use Cases Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">{t('footer.sections.useCases.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="/use-cases/games" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.games')}
                </a>
              </li>
              <li>
                <a href="/use-cases/startups" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.startups')}
                </a>
              </li>
              <li>
                <a href="/use-cases/marketing-agencies" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.marketingAgencies')}
                </a>
              </li>
              <li>
                <a href="/use-cases/health-fitness" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.healthFitness')}
                </a>
              </li>
              <li>
                <a href="/use-cases/advertising" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.advertising')}
                </a>
              </li>
              <li>
                <a href="/use-cases/affiliate-marketing" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.affiliateMarketing')}
                </a>
              </li>
              <li>
                <a href="/use-cases/ecommerce" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.ecommerce')}
                </a>
              </li>
              <li>
                <a href="/use-cases/dtc-brands" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.dtcBrands')}
                </a>
              </li>
              <li>
                <a href="/use-cases/tiktok" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.useCases.links.tiktok')}
                </a>
              </li>
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">{t('footer.sections.resources.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.resources.links.documentation')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.resources.links.helpCenter')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.resources.links.blog')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.resources.links.community')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.resources.links.templates')}
                </a>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">{t('footer.sections.company.title')}</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.company.links.about')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.company.links.careers')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.company.links.press')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.company.links.contact')}
                </a>
              </li>
              <li>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  {t('footer.sections.company.links.privacy')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/60 text-sm">
            {t('footer.copyright')}
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="/terms" className="text-white/60 hover:text-white transition-colors text-sm">
              {t('footer.termsOfService')}
            </a>
            <a href="/privacy" className="text-white/60 hover:text-white transition-colors text-sm">
              {t('footer.privacyPolicy')}
            </a>
            <a href="#" className="text-white/60 hover:text-white transition-colors text-sm">
              {t('footer.cookiePolicy')}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
