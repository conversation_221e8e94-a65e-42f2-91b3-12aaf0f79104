const ROISection = () => {
  return (
    <section className="w-full py-16 md:py-24">
      {/* ROI Statistics Section */}
      <div className=" mx-auto px-8">
        <h2 className="text-center text-3xl md:text-4xl lg:text-5xl font-extrabold mb-12 md:mb-16 text-[#E5D2FD]">
          Advertisers see{" "}
          <span
            style={{
              background:
                "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            more ROI
          </span>{" "}
          using Miuta
        </h2>

        <div className="flex flex-col md:flex-row gap-6 md:gap-8 mb-20 md:mb-32">
          <div
            className="flex-1 flex flex-col justify-center items-center border border-purple-500/30 rounded-xl px-8 py-7 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <p className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-2">
              40%+
            </p>
            <p className="text-lg md:text-2xl font-medium text-white mb-2">
              Campaign Variations
            </p>
            <p className="text-base md:text-lg text-white font-normal">
              Quickly adapts to niches
            </p>
          </div>

          <div
            className="flex-1 flex flex-col justify-center items-center border border-purple-500/30 rounded-xl px-8 py-7 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <p className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-2">
              600%
            </p>
            <p className="text-lg md:text-2xl font-medium text-white mb-2">
              Cost Reduction
            </p>
            <p className="text-base md:text-lg text-white font-normal">
              Significantly lower production costs
            </p>
          </div>

          <div
            className="flex-1 flex flex-col justify-center items-center border border-purple-500/30 rounded-xl px-8 py-7 backdrop-blur-sm"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <p className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-2">
              300%
            </p>
            <p className="text-lg md:text-2xl font-medium text-white mb-2">
              ROAS
            </p>
            <p className="text-base md:text-lg text-white font-normal">
              Lower costs and better targeting
            </p>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="text-center mb-16 md:mb-20">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#E5D2FD] mb-6 md:mb-8">
            Use{" "}
            <span
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              Miuta
            </span>{" "}
            for...
          </h2>

          <p className="text-base md:text-lg lg:text-xl text-[#B6A0D3] max-w-4xl mx-auto leading-normal font-normal">
            Social media, product launches, e-commerce ads, and brand
            storytelling—generate high-converting AI UGC videos in minutes with
            Miuta!
          </p>
        </div>

        <div className="w-full flex flex-wrap justify-between mb-12 md:mb-16">
          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              Agency
            </h3>
            <p className="text-xs text-white leading-normal">
              Create authentic AI UGC video ads for multiple clients—fast
              conversions, all at once.
            </p>
          </div>

          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              E-commerce
            </h3>
            <p className="text-xs text-white leading-normal">
              Boost conversion rates and reduce ad costs with scalable AI UGC
              content that outperforms reviews.
            </p>
          </div>

          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              Social Media
            </h3>
            <p className="text-xs text-white leading-normal">
              Create authentic-looking user generated content and product demos
              for TikTok, Instagram, and Facebook.
            </p>
          </div>

          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              D2C Brands
            </h3>
            <p className="text-xs text-white leading-normal">
              Launch unlimited A/B tests with different AI avatars and scripts,
              creating ROAS by accessing high-performing UGC videos.
            </p>
          </div>

          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              SaaS Companies
            </h3>
            <p className="text-xs text-white leading-normal">
              Generate authentic testimonial-style videos that explain complex
              features to prospects. Drive trial signups.
            </p>
          </div>

          <div
            className="w-[186px] h-[268px] rounded-xl px-4 py-8 backdrop-blur-sm transition-all duration-300 border-[#0B0019] border"
            style={{
              background:
                "linear-gradient(0deg, #130126 23.74%, #0B001A 55.92%, #38007F 114.19%)",
            }}
          >
            <div className="w-9 h-9 rounded-sm flex items-center justify-center p-[6px]"
              style={{
                border: "1px solid rgba(252, 252, 252, 0.00)",
                background:
                  "linear-gradient(270deg, rgba(0, 0, 0, 0.00) 51.68%, rgba(7, 0, 16, 0.20) 100%), linear-gradient(95deg, rgba(102, 6, 224, 0.00) 45.21%, #4E03AC 96.15%), linear-gradient(180deg, #771EE9 1.09%, #7425E3 100%)",
                boxShadow:
                  "10px 20px 40px 0 rgba(74, 103, 249, 0.20), -5px -5px 50px 0 rgba(127, 77, 248, 0.90)",
              }}
            >
              <div className="w-6 h-6 bg-white/20 rounded"></div>
            </div>
            <h3 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">
              Coaches
            </h3>
            <p className="text-xs text-white leading-normal">
              Grow your coaching programs through authentic AI avatars and
              scalable video production.
            </p>
          </div>
        </div>

        <div className="flex justify-center">
          <button className="group bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
            Get Started
            <span className="group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </button>
        </div>
      </div>
    </section>
  );
};

export { ROISection };