import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

export default function LanguageSync({ children }: { children: JSX.Element }) {
  const { i18n } = useTranslation();
  const location = useLocation();

  // 由于useEffect不会在服务端执行 所以需要在SSG处理的时候在这里单独处理一次自动切换
  if (typeof window === 'undefined') {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }
  useEffect(() => {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }, [location, i18n]);

  return children;
}
