import { But<PERSON> } from "@/components/ui/button";
import { Play, ArrowRight } from "lucide-react";

interface FeatureCTAProps {
  title: string;
  subtitle: string;
  primaryCTA?: string;
  secondaryCTA?: string;
}

export function FeatureCTA({ 
  title, 
  subtitle, 
  primaryCTA = "Start Creating Now",
  secondaryCTA = "View All Features"
}: FeatureCTAProps) {
  return (
    <section className="py-20 lg:py-28">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="p-12 rounded-3xl bg-gradient-card border border-white/10">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              <span className="gradient-text">{title}</span>
            </h2>
            
            <p className="text-lg md:text-xl text-white/80 mb-12 max-w-2xl mx-auto">
              {subtitle}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-primary to-secondary hover:shadow-glow text-white px-8 py-4 text-lg font-semibold"
              >
                <Play className="w-5 h-5 mr-2" />
                {primaryCTA}
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg"
              >
                {secondaryCTA}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
