import {
  Accordion,
  AccordionContent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useTranslation } from 'react-i18next';

export function FAQ() {
  const { t } = useTranslation();

  const faqData = [
    {
      question: t('faq.question1'),
      answer: t('faq.answer1')
    },
    {
      question: t('faq.question2'),
      answer: t('faq.answer2')
    },
    {
      question: t('faq.question3'),
      answer: t('faq.answer3')
    },
    {
      question: t('faq.question4'),
      answer: t('faq.answer4')
    },
    {
      question: t('faq.question5'),
      answer: t('faq.answer5')
    },
    {
      question: t('faq.question6'),
      answer: t('faq.answer6')
    },
    {
      question: t('faq.question7'),
      answer: t('faq.answer7')
    },
    {
      question: t('faq.question8'),
      answer: t('faq.answer8')
    },
    {
      question: t('faq.question9'),
      answer: t('faq.answer9')
    },
    {
      question: t('faq.question10'),
      answer: t('faq.answer10')
    },
    {
      question: t('faq.question11'),
      answer: t('faq.answer11')
    },
    {
      question: t('faq.question12'),
      answer: t('faq.answer12')
    },
    {
      question: t('faq.question13'),
      answer: t('faq.answer13')
    },
    {
      question: t('faq.question14'),
      answer: t('faq.answer14')
    },
    {
      question: t('faq.question15'),
      answer: t('faq.answer15')
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-card to-background">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('faq.title')}
          </h2>
          <p className="text-xl text-white/70 max-w-3xl mx-auto">
            {t('faq.subtitle')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqData.map((faq, index) => (
              <AccordionItem 
                key={index} 
                value={`item-${index}`}
                className="gradient-card border border-white/10 rounded-lg px-6"
              >
                <AccordionTrigger className="text-left text-white hover:text-white/80 text-lg font-medium py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-white/70 pb-6 leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        <div className="text-center mt-16">
          <p className="text-white/60 mb-6">
            {t('faq.stillHaveQuestions')}
          </p>
          <a 
            href="#contact" 
            className="inline-flex items-center justify-center px-8 py-3 border border-white/20 bg-white/10 backdrop-blur-sm text-white rounded-lg hover:bg-white/20 hover:border-white/30 transition-all"
          >
            {t('faq.contactSupport')}
          </a>
        </div>
      </div>
    </section>
  );
}
