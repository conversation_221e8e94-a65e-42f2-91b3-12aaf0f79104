import { useState, useEffect } from "react";

interface AnimatedTextProps {
  words: string[];
  className?: string;
  interval?: number;
}

export function AnimatedText({ words, className = "", interval = 2000 }: AnimatedTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setIsAnimating(true);
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length);
        setIsAnimating(false);
      }, 300); // Half of the animation duration
      
    }, interval);

    return () => clearInterval(timer);
  }, [words.length, interval]);

  return (
    <span className={`inline-block transition-all duration-500 ease-in-out ${className}`}>
      <span 
        className={`inline-block transition-all duration-300 ease-in-out ${
          isAnimating 
            ? 'opacity-0 transform -translate-y-2 scale-95' 
            : 'opacity-100 transform translate-y-0 scale-100'
        }`}
      >
        {words[currentIndex]}
      </span>
    </span>
  );
}
