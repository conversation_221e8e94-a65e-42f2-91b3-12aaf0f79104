import { useState, useRef, useEffect } from "react";

interface VideoItem {
  id: string;
  title: string;
  thumbnail: string;
  videoUrl?: string;
  creator?: string;
  category?: string;
}

// Mock video data - in a real app, this would come from an API
const mockVideos: VideoItem[] = [
  {
    id: "1",
    title: "Perfect",
    thumbnail: "https://picsum.photos/300/400?random=1",
    creator: "Creator 1",
    category: "Short Launches",
  },
  {
    id: "2",
    title: "Let's take a look",
    thumbnail: "https://picsum.photos/300/400?random=2",
    creator: "Creator 2",
    category: "Youtube Shorts",
  },
  {
    id: "3",
    title: "Gaming Content",
    thumbnail: "https://picsum.photos/300/400?random=3",
    creator: "Creator 3",
    category: "Instagram Reels",
  },
  {
    id: "4",
    title: "Fashion Style",
    thumbnail: "https://picsum.photos/300/400?random=4",
    creator: "Creator 4",
    category: "TikTok Videos",
  },
  {
    id: "5",
    title: "Tech Review",
    thumbnail: "/api/placeholder/300/400",
    creator: "Creator 5",
    category: "UGC Content",
  },
  {
    id: "6",
    title: "Lifestyle",
    thumbnail: "/api/placeholder/300/400",
    creator: "Creator 6",
    category: "UGC Creators",
  },
  {
    id: "7",
    title: "Travel Vlog",
    thumbnail: "/api/placeholder/300/400",
    creator: "Creator 7",
    category: "AI Actors",
  },
  {
    id: "8",
    title: "Food Review",
    thumbnail: "/api/placeholder/300/400",
    creator: "Creator 8",
    category: "Youtube Influencers",
  },
];

interface VideoCarouselProps {
  scrollSpeed?: number; // pixels per second
}

export function VideoCarousel({ scrollSpeed = 50 }: VideoCarouselProps) {
  const [isHovered, setIsHovered] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // Auto-scroll functionality
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    let lastTime = 0;

    const animate = (currentTime: number) => {
      if (!isHovered && container) {
        if (lastTime !== 0) {
          const deltaTime = currentTime - lastTime;
          const scrollDistance = (scrollSpeed * deltaTime) / 1000;

          // Check if we need to reset scroll position for infinite loop
          const maxScrollLeft = container.scrollWidth - container.clientWidth;
          if (container.scrollLeft >= maxScrollLeft) {
            container.scrollLeft = 0;
          } else {
            container.scrollLeft += scrollDistance;
          }
        }
        lastTime = currentTime;
      } else {
        lastTime = currentTime;
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isHovered, scrollSpeed]);

  return (
    <section className="relative py-16 overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-4">
            Create Videos Like These
          </h2>
          <p className="text-white/80 text-base md:text-lg max-w-2xl mx-auto px-4">
            Get inspired by our community's amazing creations
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Video Grid */}
          <div
            ref={scrollContainerRef}
            className="flex gap-3 overflow-x-auto scrollbar-hide pb-4"
            style={{
              scrollbarWidth: "none",
              msOverflowStyle: "none",
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Duplicate videos for infinite scroll effect */}
            {[...mockVideos, ...mockVideos].map((video, index) => (
              <div
                key={`${video.id}-${index}`}
                className="flex-shrink-0 carousel-fade-in"
                style={{
                  width: "216px",
                  height: "384px",
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <div className="relative rounded-xl overflow-hidden bg-gradient-card border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-card video-hover-scale h-full">
                  {/* Video Thumbnail */}
                  <div
                    className="relative bg-gradient-to-br from-primary/20 to-secondary/20 h-full"
                    style={{ aspectRatio: "9/16" }}
                  >
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Fallback to gradient background if image fails to load
                        e.currentTarget.style.display = "none";
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
