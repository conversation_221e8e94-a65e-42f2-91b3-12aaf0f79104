import { <PERSON>, <PERSON>R<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { AnimatedText } from "@/components/AnimatedText";
import { useTranslation } from "react-i18next";

export function Hero() {
  const { t } = useTranslation();

  const animatedWords = [
    t("hero.animatedWord1"),
    t("hero.animatedWord2"),
    t("hero.animatedWord3"),
    t("hero.animatedWord4"),
    t("hero.animatedWord5"),
    t("hero.animatedWord6"),
    t("hero.animatedWord7"),
  ];

  return (
    <section className="relative pt-28 pb-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-black/40" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/30 rounded-full blur-3xl opacity-20" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/30 rounded-full blur-3xl opacity-20" />

      <div className="relative container mx-auto px-6 text-center">
        {/* Badge */}
        <div className="w-full flex justify-center">
          <div
            className="max-w-[272px] flex gap-2 items-center px-3 py-2 mb-8 rounded-[200px] border border-white/20 bg-gradient-to-r from-[#3F1F6C] to-[#19082C]"
            role="banner"
            aria-label="Product announcement"
          >
            <p className="text-sm text-white/90 m-0">{t("hero.badge")}</p>
            <div
              className="flex items-center px-2 py-1 rounded-[200px]"
              style={{
                border: "1px solid #7542F7",
                boxShadow: "0 4px 30px 0 rgba(0, 0, 0, 0.02)",
              }}
              role="img"
              aria-label="App badge"
              aria-hidden="true"
            >
              <Sparkles className="w-4 h-4 text-white/90" />
              <span
                className="text-[14px font-semibold ml-1 leading-[14px] bg-clip-text text-transparent"
                style={{
                  background:
                    "linear-gradient(97deg, #7520E5 5.52%, #CF21B8 69.33%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                APP
              </span>
            </div>
          </div>
        </div>

        {/* Main Heading */}
        <div className="w-full flex flex-col justify-center gap-2">
          <h1
            className="text-4xl md:text-6xl font-extrabold leading-tight text-center font-plus-jakarta-sans mb-2 select-text bg-clip-text  text-transparent"
            style={{
              background:
                "linear-gradient(180deg, #E5D2FD 1.29%, #E8D7FE 80.28%, #AA69FE 118.42%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            Make AI UGC
            <br />
            <span
              className="mr-[1rem]"
              style={{
                background:
                  "linear-gradient(180deg, #D2B1FD 0%, #C293FD 36.06%, #B076FB 72.6%, #7550D6 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              Video Ads
            </span>
            Effortlessly
          </h1>
        </div>

        {/* Subheading */}
        <p
          className="text-xl text-[#B6A0D3] mb-10 max-w-4xl mx-auto leading-normal animate-fade-in"
          style={{ animationDelay: "0.9s" }}
        >
          Generate high-quality, scalable AI UGC videos that look real, speak
          your script, and sell your products like pros. No shoots, no delays,
          just unreal ad performance.
        </p>

        {/* CTA Buttons */}
        <div
          className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16 animate-fade-in"
          style={{ animationDelay: "1.2s" }}
        >
          <button className="px-8 py-3 bg-[#F3F3F3] text-[#7542F7] font-base font-semibold rounded-[200px] border border-[#D0D5DD]">
            Book a demo call
          </button>
          <button className="px-8 py-3 bg-purple-500 text-white font-base font-semibold rounded-[200px] border border-[#D0D5DD]">
           Create your first video
          </button>
        </div>
      </div>
    </section>
  );
}
