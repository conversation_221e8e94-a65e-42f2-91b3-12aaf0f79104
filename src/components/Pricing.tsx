import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON>, <PERSON>rkles, Crown } from "lucide-react";
import { useTranslation } from 'react-i18next';

export function Pricing() {
  const { t } = useTranslation();

  const plans = [
    {
      name: t('pricing.starterName'),
      price: t('pricing.starterPrice'),
      period: t('pricing.starterPeriod'),
      description: t('pricing.starterDescription'),
      features: [
        t('pricing.starterFeature1'),
        t('pricing.starterFeature2'),
        t('pricing.starterFeature3'),
        t('pricing.starterFeature4'),
        t('pricing.starterFeature5'),
        t('pricing.starterFeature6')
      ],
      popular: false
    },
    {
      name: t('pricing.professionalName'),
      price: t('pricing.professionalPrice'),
      period: t('pricing.professionalPeriod'),
      description: t('pricing.professionalDescription'),
      features: [
        t('pricing.professionalFeature1'),
        t('pricing.professionalFeature2'),
        t('pricing.professionalFeature3'),
        t('pricing.professionalFeature4'),
        t('pricing.professionalFeature5'),
        t('pricing.professionalFeature6'),
        t('pricing.professionalFeature7'),
        t('pricing.professionalFeature8')
      ],
      popular: true
    },
    {
      name: t('pricing.enterpriseName'),
      price: t('pricing.enterprisePrice'),
      period: t('pricing.enterprisePeriod'),
      description: t('pricing.enterpriseDescription'),
      features: [
        t('pricing.enterpriseFeature1'),
        t('pricing.enterpriseFeature2'),
        t('pricing.enterpriseFeature3'),
        t('pricing.enterpriseFeature4'),
        t('pricing.enterpriseFeature5'),
        t('pricing.enterpriseFeature6'),
        t('pricing.enterpriseFeature7'),
        t('pricing.enterpriseFeature8'),
        t('pricing.enterpriseFeature9')
      ],
      popular: false
    }
  ];

  return (
    <section id="pricing" className="py-24 relative">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('pricing.title')}
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {t('pricing.subtitle')}
          </p>
        </div>

        {/* Pricing Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <Card 
              key={index}
              className={`gradient-card border-white/10 p-8 relative ${
                plan.popular 
                  ? 'scale-105 shadow-glow border-primary/50' 
                  : 'hover:scale-105'
              } transition-all duration-300`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="gradient-button px-4 py-2 rounded-full flex items-center space-x-2">
                    <Crown className="w-4 h-4 text-white" />
                    <span className="text-white text-sm font-semibold">{t('pricing.mostPopular')}</span>
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-white/60 mb-6">{plan.description}</p>
                <div className="flex items-baseline justify-center">
                  <span className="text-5xl font-bold text-white">${plan.price}</span>
                  <span className="text-white/60 ml-2">/{plan.period}</span>
                </div>
              </div>

              <div className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-primary flex-shrink-0" />
                    <span className="text-white/80">{feature}</span>
                  </div>
                ))}
              </div>

              <Button 
                variant={plan.popular ? "default" : "outline"} 
                className="w-full"
                size="lg"
              >
                {plan.popular && <Sparkles className="w-4 h-4 mr-2" />}
                {t('pricing.getStarted')}
              </Button>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-white/60 mb-6">
            {t('pricing.customSolution')}
          </p>
          <Button variant="ghost" size="lg">
            {t('pricing.contactSales')}
          </Button>
        </div>
      </div>
    </section>
  );
}
