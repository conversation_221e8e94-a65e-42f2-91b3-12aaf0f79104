import { useEffect, useState, type RefAttributes } from "react";
import { useTranslation } from "react-i18next";
import { Link, type LinkProps } from "react-router-dom";

export default (props: LinkProps & RefAttributes<HTMLAnchorElement>) => {
    const { i18n } = useTranslation();
    const [path, setPath] = useState(props.to);

    useEffect(() => {
        const to = props.to as string;
        const url = to.startsWith("http")
            ? new URL(to)
            : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
        setPath(url.toString().replace("http://localhost", ""));
    }, [i18n.language, props.to]);

    return <Link {...props} to={path}>{props.children}</Link>
}
