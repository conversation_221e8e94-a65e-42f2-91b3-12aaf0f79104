import { useEffect, useState, type AnchorHTMLAttributes } from "react";
import { useTranslation } from "react-i18next";

export default (props: AnchorHTMLAttributes<HTMLAnchorElement>) => {
    const { i18n } = useTranslation();
    const [path, setPath] = useState(props.href);

    useEffect(() => {
        const to = props.href as string;
        const url = to.startsWith("http")
            ? new URL(to)
            : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
        setPath(url.toString().replace("http://localhost", ""));
    }, [i18n.language, props.href]);

    return <a {...props} href={path}>{props.children}</a>
}
