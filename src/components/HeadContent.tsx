import { Head } from "vite-react-ssg";

export function HeadContent({
  title,
  description,
  keywords,
  author = "Miuta.ai",
  robots = "index, follow",
  copyright = "Miuta.ai",
  url,
  image,
  twitter = "@你的推特账号",
  schemas,
  languages = [],
}: {
  title: string;
  description: string;
  keywords: string;
  author?: string;
  robots?: string;
  copyright?: string;
  url: string;
  image: string;
  twitter?: string;
  schemas: {}[];
  languages?: string[];
}) {
  return (
    <Head>
      {/** 字符集 */}
      <meta charSet="UTF-8" />
      {/** 移动端适配 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      {/** 基本SEO标签 */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <meta name="robots" content={robots} />
      <meta name="copyright" content={copyright} />
      <meta property="og:title" content={title} />
      {/** 社交媒体分享 微信/QQ/微博/FB */}
      <meta property="og:description" content={description} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={url} />
      <meta
        property="og:image"
        content={`${image}?v=${import.meta.env.VITE_APP_HASH}`}
      />
      <meta property="og:site_name" content={title} />
      {/** 社交媒体分享 Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta
        name="twitter:image"
        content={`${image}?v=${import.meta.env.VITE_APP_HASH}`}
      />
      <meta name="twitter:site" content={twitter} />
      {/** 图标 */}
      <link
        rel="icon"
        href={`/favicon.ico?v=${import.meta.env.VITE_APP_HASH}`}
      />
      <link
        rel="apple-touch-icon"
        href={`/apple-touch-icon.png?v=${import.meta.env.VITE_APP_HASH}`}
      />
      {/** 站点地图 */}
      <link
        rel="sitemap"
        type="application/xml"
        title="Sitemap"
        href="/sitemap.xml"
      />
      {/** 规范链接 防止重复收录 相同内容页面href相同 告知搜索引擎是一个页面 */}
      <link rel="canonical" href={url} />
      {/** 多语言链接 */}
      <link rel="alternate" href={url} hrefLang="x-default" />
      <link rel="alternate" href={url} hrefLang="en" />
      {languages.map(
        (language) =>
          language !== "en" && (
            <link rel="alternate" href={`${url}${language}/`} hrefLang={language} />
          )
      )}
      {/** PWA 网站清单 */}
      <link rel="manifest" href="/manifest.webmanifest" />
      {/**  结构化数据（JSON-LD，提升富媒体展示） */}
      {schemas.map((schemaItem, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schemaItem, null, 2),
          }}
        />
      ))}
    </Head>
  );
}
