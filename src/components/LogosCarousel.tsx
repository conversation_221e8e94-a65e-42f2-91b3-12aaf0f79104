import { useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CompanyLogo {
  id: string;
  name: string;
  logo: string;
  url?: string;
}

// Mock company logos data based on the design mockup
const mockLogos: CompanyLogo[] = [
  { id: "1", name: "Boltshift", logo: "/api/placeholder/120/40" },
  { id: "2", name: "Boltshift", logo: "/api/placeholder/120/40" },
  { id: "3", name: "Lightbox", logo: "/api/placeholder/120/40" },
  { id: "4", name: "Lightbox", logo: "/api/placeholder/120/40" },
  { id: "5", name: "FeatherDev", logo: "/api/placeholder/120/40" },
  { id: "6", name: "Spherule", logo: "/api/placeholder/120/40" },
  { id: "7", name: "GlobalBank", logo: "/api/placeholder/120/40" },
  { id: "8", name: "<PERSON><PERSON><PERSON><PERSON>", logo: "/api/placeholder/120/40" },
  { id: "9", name: "Company 9", logo: "/api/placeholder/120/40" },
  { id: "10", name: "Company 10", logo: "/api/placeholder/120/40" },
  { id: "11", name: "Company 11", logo: "/api/placeholder/120/40" },
  { id: "12", name: "Company 12", logo: "/api/placeholder/120/40" },
];

export function LogosCarousel() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -300,
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 300,
        behavior: "smooth",
      });
    }
  };

  return (
    <section className="relative py-12 overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-6 md:mb-8">
          <div className="flex items-center justify-center gap-2 mb-4 px-4">
            <span className="text-xl md:text-2xl">🔥</span>
            <h2 className="text-lg md:text-xl lg:text-2xl font-semibold text-white text-center">
              The best performance marketing teams are using Miuta
            </h2>
            <span className="text-xl md:text-2xl">🔥</span>
          </div>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons - Hidden on mobile */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white border-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={scrollLeft}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white border-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={scrollRight}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>

          {/* Logos Container */}
          <div className="group">
            <div
              ref={scrollContainerRef}
              className="flex gap-4 md:gap-8 overflow-x-auto scrollbar-hide pb-4 items-center"
              style={{
                scrollbarWidth: "none",
                msOverflowStyle: "none",
              }}
            >
              {mockLogos.map((logo) => (
                <div
                  key={logo.id}
                  className="flex-shrink-0 flex items-center justify-center"
                >
                  <div className="w-24 h-10 md:w-32 md:h-12 flex items-center justify-center bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-lg cursor-pointer logo-hover">
                    <img
                      src={logo.logo}
                      alt={logo.name}
                      className="max-w-full max-h-full object-contain opacity-60 hover:opacity-80 transition-opacity"
                      onError={(e) => {
                        // Fallback to text if image fails to load
                        e.currentTarget.style.display = "none";
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.innerHTML = `<span class="text-white/60 text-sm font-medium">${logo.name}</span>`;
                        }
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
