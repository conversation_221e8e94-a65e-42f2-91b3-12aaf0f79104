import { useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Tag {
  id: string;
  name: string;
  color?: string;
}

// Mock tags data based on the design mockup
const mockTags: Tag[] = [
  { id: "1", name: "Short Launches" },
  { id: "2", name: "Youtube Shorts" },
  { id: "3", name: "Instagram Reels" },
  { id: "4", name: "TikTok Videos" },
  { id: "5", name: "UGC Content" },
  { id: "6", name: "UGC Creators" },
  { id: "7", name: "AI Actors" },
  { id: "8", name: "Youtube Influencers" },
  { id: "9", name: "Amazon Influencers" },
  { id: "10", name: "TikTok Creators" },
  { id: "11", name: "Amazon Shoppable" },
  { id: "12", name: "TikTok Videos" },
  { id: "13", name: "UGC Creators" },
  { id: "14", name: "AI Actors" },
  { id: "15", name: "Youtube Influencers" },
  { id: "16", name: "Amazon Influencers" },
];

export function TagsCarousel() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -300,
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 300,
        behavior: "smooth",
      });
    }
  };

  return (
    <section className="relative py-8 overflow-hidden">
      <div className="container mx-auto px-6">
        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons - Hidden on mobile */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white border-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={scrollLeft}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white border-white/20 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={scrollRight}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>

          {/* Tags Container */}
          <div className="group">
            <div
              ref={scrollContainerRef}
              className="flex gap-3 overflow-x-auto scrollbar-hide pb-2"
              style={{
                scrollbarWidth: "none",
                msOverflowStyle: "none",
              }}
            >
              {mockTags.map((tag) => (
                <div key={tag.id} className="flex-shrink-0">
                  <span className="inline-block px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium text-white/80 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 rounded-full cursor-pointer tag-hover hover:text-white">
                    {tag.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
