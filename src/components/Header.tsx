import { Play, Menu, X, ChevronDown } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function Header() {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUseCasesOpen, setIsUseCasesOpen] = useState(false);
  const [isFeaturesOpen, setIsFeaturesOpen] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [featureTimeout, setFeatureTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const useCases = [
    { name: t("header.useCasesDropdown.games"), url: "/use-cases/games" },
    { name: t("header.useCasesDropdown.startups"), url: "/use-cases/startups" },
    {
      name: t("header.useCasesDropdown.marketingAgencies"),
      url: "/use-cases/marketing-agencies",
    },
    {
      name: t("header.useCasesDropdown.healthFitness"),
      url: "/use-cases/health-fitness",
    },
    {
      name: t("header.useCasesDropdown.advertising"),
      url: "/use-cases/advertising",
    },
    {
      name: t("header.useCasesDropdown.affiliateMarketing"),
      url: "/use-cases/affiliate-marketing",
    },
    {
      name: t("header.useCasesDropdown.ecommerce"),
      url: "/use-cases/ecommerce",
    },
    {
      name: t("header.useCasesDropdown.dtcBrands"),
      url: "/use-cases/dtc-brands",
    },
    { name: t("header.useCasesDropdown.tiktok"), url: "/use-cases/tiktok" },
  ];

  const features = [
    {
      name: t("header.featuresDropdown.aiAvatars"),
      url: "/features/ai-avatars",
      description: "Ultra-realistic AI actors",
    },
    {
      name: t("header.featuresDropdown.aiScriptGenerator"),
      url: "/features/ai-script-generator",
      description: "AI-powered script creation",
    },
    {
      name: t("header.featuresDropdown.aiInstagramAds"),
      url: "/features/ai-instagram-ads",
      description: "Instagram-optimized ads",
    },
    {
      name: t("header.featuresDropdown.aiFacebookAds"),
      url: "/features/ai-facebook-ads",
      description: "Facebook-optimized ads",
    },
    {
      name: t("header.featuresDropdown.aiTiktokAds"),
      url: "/features/ai-tiktok-ads",
      description: "TikTok-optimized ads",
    },
  ];

  const handleMouseEnter = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    setIsUseCasesOpen(true);
  };

  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setIsUseCasesOpen(false);
    }, 300);
    setHoverTimeout(timeout);
  };

  const handleFeaturesMouseEnter = () => {
    if (featureTimeout) {
      clearTimeout(featureTimeout);
      setFeatureTimeout(null);
    }
    setIsFeaturesOpen(true);
  };

  const handleFeaturesMouseLeave = () => {
    const timeout = setTimeout(() => {
      setIsFeaturesOpen(false);
    }, 300);
    setFeatureTimeout(timeout);
  };

  return (
    <header className="z-50 w-full fixed top-0 bg-black">
      <nav className="container mx-auto px-6 py-4 flex items-center justify-between">
        {/* Logo */}
        <a
          href="/"
          className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
        >
          <div className="w-8 h-8 rounded-lg gradient-button flex items-center justify-center">
            <Play className="w-4 h-4 text-white" />
          </div>
          <span className="text-xl font-bold text-white">Miuta</span>
        </a>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <div
            className="relative"
            onMouseEnter={handleFeaturesMouseEnter}
            onMouseLeave={handleFeaturesMouseLeave}
          >
            <button
              className={`flex items-center transition-colors duration-200 ${
                isFeaturesOpen ? "text-white" : "text-white/80 hover:text-white"
              }`}
            >
              {t("header.features")}
              <ChevronDown
                className={`ml-1 h-4 w-4 transition-transform duration-200 ${
                  isFeaturesOpen ? "rotate-180" : ""
                }`}
              />
            </button>
            <div
              className={`absolute top-full left-0 mt-2 w-80 transition-all duration-300 ease-out ${
                isFeaturesOpen
                  ? "opacity-100 visible translate-y-0"
                  : "opacity-0 invisible -translate-y-2"
              }`}
            >
              <div className="bg-gradient-card backdrop-blur-xl border border-white/20 rounded-xl shadow-card overflow-hidden">
                <div className="py-3">
                  {features.map((feature, index) => (
                    <a
                      key={feature.name}
                      href={feature.url}
                      className="block px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 border-l-2 border-transparent hover:border-primary/50"
                      style={{
                        animationDelay: `${index * 50}ms`,
                      }}
                    >
                      <div className="font-medium">{feature.name}</div>
                      <div className="text-sm text-white/60 mt-1">
                        {feature.description}
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div
            className="relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <button
              className={`flex items-center transition-colors duration-200 ${
                isUseCasesOpen ? "text-white" : "text-white/80 hover:text-white"
              }`}
            >
              {t("header.useCases")}
              <ChevronDown
                className={`ml-1 h-4 w-4 transition-transform duration-200 ${
                  isUseCasesOpen ? "rotate-180" : ""
                }`}
              />
            </button>
            <div
              className={`absolute top-full left-0 mt-2 w-64 transition-all duration-300 ease-out ${
                isUseCasesOpen
                  ? "opacity-100 visible translate-y-0"
                  : "opacity-0 invisible -translate-y-2"
              }`}
            >
              <div className="bg-gradient-card backdrop-blur-xl border border-white/20 rounded-xl shadow-card overflow-hidden">
                <div className="py-3">
                  {useCases.map((useCase, index) => (
                    <a
                      key={useCase.name}
                      href={useCase.url}
                      className="block px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 border-l-2 border-transparent hover:border-primary/50"
                      style={{
                        animationDelay: `${index * 50}ms`,
                      }}
                    >
                      <div className="font-medium">{useCase.name}</div>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <a
            href="#pricing"
            className="text-white/80 hover:text-white transition-colors"
          >
            {t("header.pricing")}
          </a>
          <a
            href="#about"
            className="text-white/80 hover:text-white transition-colors"
          >
            {t("header.about")}
          </a>
        </div>

        {/* Desktop CTA */}
        <div className="hidden md:flex items-center space-x-4">
          <button className="premium-gradient-btn">
            {t("header.startCreating")}
          </button>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </nav>

      {/* Mobile Menu */}
      <div
        className={`md:hidden absolute top-full left-0 w-full transition-all duration-300 ease-out z-50 ${
          isMenuOpen
            ? "opacity-100 visible translate-y-0"
            : "opacity-0 invisible -translate-y-4"
        }`}
      >
        <div className="bg-gradient-card backdrop-blur-xl border-t border-white/20">
          <div className="container mx-auto px-6 py-6 space-y-4">
            <div className="py-2">
              <div className="text-white font-semibold mb-3 px-2">
                {t("header.features")}
              </div>
              <div className="space-y-1">
                {features.map((feature, index) => (
                  <a
                    key={feature.name}
                    href={feature.url}
                    className="block text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 py-2 px-4 rounded-lg border-l-2 border-transparent hover:border-primary/50"
                    onClick={() => setIsMenuOpen(false)}
                    style={{
                      animationDelay: `${index * 30}ms`,
                    }}
                  >
                    <div className="font-medium">{feature.name}</div>
                    <div className="text-sm text-white/60 mt-1">
                      {feature.description}
                    </div>
                  </a>
                ))}
              </div>
            </div>
            <div className="py-2">
              <div className="text-white font-semibold mb-3 px-2">
                {t("header.useCases")}
              </div>
              <div className="space-y-1">
                {useCases.map((useCase, index) => (
                  <a
                    key={useCase.name}
                    href={useCase.url}
                    className="block text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 py-2 px-4 rounded-lg border-l-2 border-transparent hover:border-primary/50"
                    onClick={() => setIsMenuOpen(false)}
                    style={{
                      animationDelay: `${(features.length + index) * 30}ms`,
                    }}
                  >
                    <div className="font-medium">{useCase.name}</div>
                  </a>
                ))}
              </div>
            </div>
            <div className="py-2 space-y-2">
              <a
                href="#pricing"
                className="block text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 py-2 px-4 rounded-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("header.pricing")}
              </a>
              <a
                href="#about"
                className="block text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 py-2 px-4 rounded-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("header.about")}
              </a>
            </div>
            <div className="pt-4 border-t border-white/20 space-y-3">
              <button
                className="premium-gradient-btn w-full"
                onClick={() => setIsMenuOpen(false)}
                style={{ width: "100%" }}
              >
                {t("header.startCreating")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
