import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export function useI18nNavigate() {
  const navigate = useNavigate();
  const { i18n } = useTranslation();

  return (to: string, options?: { replace?: boolean }) => {
    const url = to.startsWith("http")
        ? new URL(to)
        : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
    navigate(url.toString().replace("http://localhost", ""), options);
  };
}
