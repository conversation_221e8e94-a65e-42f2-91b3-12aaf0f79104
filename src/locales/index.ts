import i18n from "i18next";
import { initReactI18next } from "react-i18next";
// import Backend from 'i18next-http-backend';
import translation from "./en/translation.json";
import features from "./en/features.json";
import useCases from "./en/use-cases.json";
import privacy from "./en/privacy.json";
import terms from "./en/terms.json";

const lng =
  typeof window !== "undefined"
    ? import.meta.env.VITE_APP_I18N.split(",").find((lang: string) =>
        window.location.pathname.endsWith(`/${lang}/`)
      )
    : undefined;

export const initI18n = () =>
  i18n
    // .use(Backend)
    .use(initReactI18next)
    .init({
      resources: {
        en: {
          translation: translation,
          features: features,
          "use-cases": useCases,
          privacy: privacy,
          terms: terms,
        },
      },
      lng: lng,
      fallbackLng:
        typeof window !== "undefined"
          ? import.meta.env.VITE_APP_I18N_DEFAULT
          : process.env.VITE_APP_I18N_DEFAULT,
      ns: ["translation", "features", "use-cases"], // 只用一个命名空间
      defaultNS: "translation", // 默认命名空间
      // backend: {
      //     loadPath: typeof window !== 'undefined' ? `/locales/{{lng}}/{{ns}}.json?${typeof window !== 'undefined' ? import.meta.env.VITE_APP_HASH : process.env.VITE_APP_HASH}` : `/locales/{{lng}}/{{ns}}.json`,
      // },
      react: {
        useSuspense: false,
      },
    });

export default i18n;
