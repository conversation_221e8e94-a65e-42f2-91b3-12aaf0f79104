@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Sophisticated dark theme with vibrant gradients */
    --background: 215 28% 7%; /* Deep dark blue-gray */
    --foreground: 0 0% 98%; /* Pure white text */

    --card: 215 25% 10%; /* Slightly lighter dark */
    --card-foreground: 0 0% 98%;

    --popover: 215 25% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 270 100% 65%; /* Vibrant purple */
    --primary-foreground: 0 0% 100%;

    --secondary: 195 100% 55%; /* Electric blue */
    --secondary-foreground: 0 0% 100%;

    --muted: 215 20% 15%; /* Muted dark */
    --muted-foreground: 0 0% 75%;

    --accent: 320 100% 65%; /* Hot pink accent */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 15% 20%; /* Subtle border */
    --input: 215 20% 12%; /* Dark input */
    --ring: 270 100% 65%; /* Purple focus ring */

    /* Advanced gradient system */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(270, 100%, 65%) 0%,
      hsl(320, 100%, 65%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(195, 100%, 55%) 0%,
      hsl(270, 100%, 65%) 100%
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(320, 100%, 65%) 0%,
      hsl(40, 100%, 60%) 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(215, 28%, 4%) 0%,
      hsl(215, 28%, 8%) 50%,
      hsl(215, 25%, 6%) 100%
    );
    --gradient-card: linear-gradient(
      135deg,
      hsl(215, 25%, 8%) 0%,
      hsl(215, 20%, 12%) 100%
    );
    --gradient-button: linear-gradient(
      135deg,
      hsl(270, 100%, 65%) 0%,
      hsl(320, 100%, 65%) 100%
    );

    /* Text gradients for important words */
    --gradient-text-primary: linear-gradient(
      135deg,
      hsl(270, 100%, 70%) 0%,
      hsl(320, 100%, 70%) 100%
    );
    --gradient-text-secondary: linear-gradient(
      135deg,
      hsl(195, 100%, 60%) 0%,
      hsl(270, 100%, 70%) 100%
    );
    --gradient-text-accent: linear-gradient(
      135deg,
      hsl(320, 100%, 70%) 0%,
      hsl(40, 100%, 65%) 100%
    );

    /* Professional shadows */
    --shadow-glow: 0 0 40px hsl(270 100% 65% / 0.3);
    --shadow-card: 0 20px 60px hsl(215 28% 4% / 0.8);
    --shadow-button: 0 10px 30px hsl(270 100% 65% / 0.4);

    --radius: 0.75rem;

    /* Professional animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 215 25% 10%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 270 100% 65%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 20% 15%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 215 15% 20%;
    --sidebar-ring: 270 100% 65%;
  }

  .dark {
    /* Same sophisticated dark theme for consistency */
    --background: 215 28% 7%;
    --foreground: 0 0% 98%;
    --card: 215 25% 10%;
    --card-foreground: 0 0% 98%;
    --popover: 215 25% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 270 100% 65%;
    --primary-foreground: 0 0% 100%;
    --secondary: 195 100% 55%;
    --secondary-foreground: 0 0% 100%;
    --muted: 215 20% 15%;
    --muted-foreground: 0 0% 75%;
    --accent: 320 100% 65%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 215 15% 20%;
    --input: 215 20% 12%;
    --ring: 270 100% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-hero);
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      sans-serif;
  }
}

@layer utilities {
  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-card {
    background: var(--gradient-card);
    border: 1px solid hsl(var(--border));
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(20px);
  }

  .gradient-button {
    background: var(--gradient-button);
    box-shadow: var(--shadow-button);
    transition: var(--transition-smooth);
  }

  .gradient-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  /* Gradient text utilities for important words */
  .text-gradient {
    background: var(--gradient-text-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .text-gradient-secondary {
    background: var(--gradient-text-secondary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .text-gradient-accent {
    background: var(--gradient-text-accent);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Animated gradient background */
  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Professional hover effects */
  .hover-glow {
    transition: var(--transition-smooth);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* Text effects for important content */
  .highlight-text {
    position: relative;
    display: inline-block;
  }

  .highlight-text::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
  }

  .highlight-text:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }

  /* Carousel and scroll animations */
  .carousel-fade-in {
    animation: carousel-fade-in 0.6s ease-out forwards;
  }

  @keyframes carousel-fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Hide scrollbars but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Video hover effects */
  .video-hover-scale {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .video-hover-scale:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-glow);
  }

  /* Tag and logo hover effects */
  .tag-hover {
    transition: all 0.2s ease;
  }

  .tag-hover:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px hsl(270 100% 65% / 0.3);
  }

  .logo-hover {
    transition: all 0.2s ease;
  }

  .logo-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px hsl(0 0% 0% / 0.2);
  }

  /* Premium gradient button with high-end effects */
  .premium-gradient-btn {
    width: 167px;
    height: 44px;
    flex-shrink: 0;
    border-radius: 22px;
    border: none;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    color: white;
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Complex gradient background */
    background: linear-gradient(
        90deg,
        rgba(117, 66, 247, 0) 31.7%,
        rgba(117, 66, 247, 0.2) 76.03%
      ),
      linear-gradient(270deg, rgba(0, 0, 0, 0) 51.68%, rgba(7, 0, 16, 0.2) 100%),
      linear-gradient(90deg, rgba(102, 6, 224, 0) 81.78%, #4e03ac 100%),
      linear-gradient(180deg, #771ee9 1.09%, #7425e3 100%);

    /* Premium shadow effects */
    filter: drop-shadow(-5px -5px 50px rgba(127, 77, 248, 0.9))
      drop-shadow(10px 20px 40px rgba(74, 103, 249, 0.2));

    /* Subtle border */
    box-shadow: inset 0 0 0 0.5px rgba(252, 252, 252, 0.1);
  }

  .premium-gradient-btn::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    border-radius: 1px;
  }

  .premium-gradient-btn::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    border-radius: 0.5px;
  }

  .premium-gradient-btn:hover {
    transform: translateY(-2px) scale(1.02);
    filter: drop-shadow(-8px -8px 60px rgba(127, 77, 248, 1))
      drop-shadow(12px 24px 50px rgba(74, 103, 249, 0.3));
  }

  .premium-gradient-btn:active {
    transform: translateY(-1px) scale(1.01);
    filter: drop-shadow(-3px -3px 40px rgba(127, 77, 248, 0.8))
      drop-shadow(8px 16px 30px rgba(74, 103, 249, 0.15));
  }
}
