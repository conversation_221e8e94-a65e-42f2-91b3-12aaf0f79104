import { Header } from "@/components/Header";
import { <PERSON> } from "@/components/Hero";
import { VideoCarousel } from "@/components/VideoCarousel";
import { TagsCarousel } from "@/components/TagsCarousel";
import { LogosCarousel } from "@/components/LogosCarousel";
import { ROISection } from "@/components/ROISection";
import { Features } from "@/components/Features";
import { Pricing } from "@/components/Pricing";
import { FAQ } from "@/components/FAQ";
import { Footer } from "@/components/Footer";
import { HeadContent } from "@/components/HeadContent";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";
import { useTranslation } from "react-i18next";

const Index = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-hero max-w-[1440px] mx-auto">
      <HeadContent
        title={t("headContent.title")}
        description={t("headContent.description")}
        keywords={t("headContent.keywords")}
        url="https://miuta.ai/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${
          import.meta.env.VITE_APP_HASH
        }`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
      />
      <Header />
      <Hero />
      <VideoCarousel />
      <TagsCarousel />
      <LogosCarousel />
      <ROISection />
      {/* <Features /> */}
      {/* <Pricing /> */}
      <FAQ />
      <Footer />
    </div>
  );
};

export default Index;
