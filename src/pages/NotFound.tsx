import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { baseOrganization, websiteSchema } from "@/lib/schema";
import { HeadContent } from "@/components/HeadContent";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <HeadContent
        title="Page Not Found - Miuta AI Video Creation Platform"
        description="The page you're looking for doesn't exist. Explore our AI video creation features and tools to create amazing marketing videos."
        keywords="404, page not found, Miuta, AI video creation"
        url="https://miuta.ai/404/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png`}
        schemas={[baseOrganization, websiteSchema]}
      />
      
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <p className="text-xl text-gray-600 mb-4">Oops! Page not found</p>
        <a href="/" className="text-blue-500 hover:text-blue-700 underline">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
