import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Users, Trophy, Target, Zap, BarChart } from "lucide-react";
import { useTranslation, Trans } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";

const Games = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Users,
      title: t("games.feature1Title"),
      description: t("games.feature1Description"),
    },
    {
      icon: Trophy,
      title: t("games.feature2Title"),
      description: t("games.feature2Description"),
    },
    {
      icon: Target,
      title: t("games.feature3Title"),
      description: t("games.feature3Description"),
    },
    {
      icon: Zap,
      title: t("games.feature4Title"),
      description: t("games.feature4Description"),
    },
  ];

  const benefits = [
    t("games.benefit1"),
    t("games.benefit2"),
    t("games.benefit3"),
    t("games.benefit4"),
    t("games.benefit5"),
    t("games.benefit6"),
  ];

  const useCases = [
    {
      title: t("games.useCase1Title"),
      description: t("games.useCase1Description"),
    },
    {
      title: t("games.useCase2Title"),
      description: t("games.useCase2Description"),
    },
    {
      title: t("games.useCase3Title"),
      description: t("games.useCase3Description"),
    },
    {
      title: t("games.useCase4Title"),
      description: t("games.useCase4Description"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("games.meta.title")}
        description={t("games.meta.description")}
        keywords={t("games.meta.keywords")}
        url="https://miuta.ai/use-cases/games/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Gaming", url: "https://miuta.ai/use-cases/games/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for Gaming",
            "Professional video marketing solutions for gaming companies and developers",
            ["Game Trailers", "Character Showcases", "Gameplay Videos"]
          ),
        ]}
      />
      <Header />

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("games.title")}
            <span className="text-gradient block mt-2">
              {t("games.subtitle")}
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            <Trans i18nKey="games.description">
              Create viral gaming content with{" "}
              <a
                href="/features/ai-avatars"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                AI actors
              </a>
              . From game trailers to player tutorials, generate engaging videos
              that boost player acquisition and retention at scale. Perfect for{" "}
              <a
                href="/features/ai-tiktok-ads"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                TikTok marketing campaigns
              </a>{" "}
              and{" "}
              <a
                href="/use-cases/advertising"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                performance advertising
              </a>
              .
            </Trans>
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Play className="mr-2 h-5 w-5" />
              {t("games.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("games.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("games.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("games.benefitsTitle")}
            </h2>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <BarChart className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">
                {t("games.resultsTitle")}
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("games.result1Value")}
                  </div>
                  <div className="text-white/70">{t("games.result1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("games.result2Value")}
                  </div>
                  <div className="text-white/70">{t("games.result2Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("games.result3Value")}
                  </div>
                  <div className="text-white/70">{t("games.result3Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("games.result4Value")}
                  </div>
                  <div className="text-white/70">{t("games.result4Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("games.useCasesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10"
              >
                <h3 className="text-2xl font-bold text-white mb-4">
                  {useCase.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {useCase.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("games.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("games.ctaDescription")}
          </p>
          <Button size="lg" className="text-lg px-8 py-6">
            <Play className="mr-2 h-5 w-5" />
            {t("games.ctaButton")}
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Games;
