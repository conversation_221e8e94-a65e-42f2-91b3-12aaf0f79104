import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { Hash, Music, Play, TrendingUp, Users, Video, Zap } from "lucide-react";

const Tiktok = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: TrendingUp,
      title: t("tiktok.feature1Title"),
      description: t("tiktok.feature1Description"),
    },
    {
      icon: Music,
      title: t("tiktok.feature2Title"),
      description: t("tiktok.feature2Description"),
    },
    {
      icon: Users,
      title: t("tiktok.feature3Title"),
      description: t("tiktok.feature3Description"),
    },
    {
      icon: Hash,
      title: t("tiktok.feature4Title"),
      description: t("tiktok.feature4Description"),
    },
  ];

  const benefits = [
    t("tiktok.benefit1"),
    t("tiktok.benefit2"),
    t("tiktok.benefit3"),
    t("tiktok.benefit4"),
    t("tiktok.benefit5"),
    t("tiktok.benefit6"),
  ];

  const contentTypes = [
    {
      title: t("tiktok.contentType1Title"),
      description: t("tiktok.contentType1Description"),
    },
    {
      title: t("tiktok.contentType2Title"),
      description: t("tiktok.contentType2Description"),
    },
    {
      title: t("tiktok.contentType3Title"),
      description: t("tiktok.contentType3Description"),
    },
    {
      title: t("tiktok.contentType4Title"),
      description: t("tiktok.contentType4Description"),
    },
    {
      title: t("tiktok.contentType5Title"),
      description: t("tiktok.contentType5Description"),
    },
    {
      title: t("tiktok.contentType6Title"),
      description: t("tiktok.contentType6Description"),
    },
  ];

  const strategies = [
    {
      title: t("tiktok.strategy1Title"),
      description: t("tiktok.strategy1Description"),
      frequency: t("tiktok.strategy1Frequency"),
    },
    {
      title: t("tiktok.strategy2Title"),
      description: t("tiktok.strategy2Description"),
      frequency: t("tiktok.strategy2Frequency"),
    },
    {
      title: t("tiktok.strategy3Title"),
      description: t("tiktok.strategy3Description"),
      frequency: t("tiktok.strategy3Frequency"),
    },
    {
      title: t("tiktok.strategy4Title"),
      description: t("tiktok.strategy4Description"),
      frequency: t("tiktok.strategy4Frequency"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("tiktok.meta.title")}
        description={t("tiktok.meta.description")}
        keywords={t("tiktok.meta.keywords")}
        url="https://miuta.ai/use-cases/tiktok/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "TikTok", url: "https://miuta.ai/use-cases/tiktok/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Content for TikTok Marketing",
            "Professional TikTok video content creation with AI technology",
            ["Viral Content", "Brand Videos", "Trend-based Content"]
          ),
        ]}
      />
      <Header />

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("tiktok.title")}
            <span className="text-gradient block mt-2">
              {t("tiktok.subtitle")}
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("tiktok.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Video className="mr-2 h-5 w-5" />
              {t("tiktok.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("tiktok.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("tiktok.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("tiktok.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("tiktok.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Zap className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">
                {t("tiktok.performanceTitle")}
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("tiktok.performance1Value")}
                  </div>
                  <div className="text-white/70">
                    {t("tiktok.performance1Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("tiktok.performance2Value")}
                  </div>
                  <div className="text-white/70">
                    {t("tiktok.performance2Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("tiktok.performance3Value")}
                  </div>
                  <div className="text-white/70">
                    {t("tiktok.performance3Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("tiktok.performance4Value")}
                  </div>
                  <div className="text-white/70">
                    {t("tiktok.performance4Label")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Types Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("tiktok.contentTypesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {contentTypes.map((type, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10"
              >
                <h3 className="text-xl font-bold text-white mb-4">
                  {type.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {type.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Strategy Framework */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("tiktok.frameworkTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {strategies.map((strategy, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10 text-center"
              >
                <div className="text-2xl font-bold text-gradient mb-4">
                  {strategy.frequency}
                </div>
                <h3 className="text-xl font-bold text-white mb-4">
                  {strategy.title}
                </h3>
                <p className="text-white/70">{strategy.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Story */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              {t("tiktok.successStory")}
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">J</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">
                  {t("tiktok.successAuthor")}
                </div>
                <div className="text-white/60">{t("tiktok.successTitle")}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* TikTok Video Examples */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("tiktok.videoExamplesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-[9/16] bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">
                {t("tiktok.videoExample1Title")}
              </h3>
              <p className="text-white/70 text-sm">
                {t("tiktok.videoExample1Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-[9/16] bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">
                {t("tiktok.videoExample2Title")}
              </h3>
              <p className="text-white/70 text-sm">
                {t("tiktok.videoExample2Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-[9/16] bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">
                {t("tiktok.videoExample3Title")}
              </h3>
              <p className="text-white/70 text-sm">
                {t("tiktok.videoExample3Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-[9/16] bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">
                {t("tiktok.videoExample4Title")}
              </h3>
              <p className="text-white/70 text-sm">
                {t("tiktok.videoExample4Description")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* TikTok Algorithm Tips */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            {t("tiktok.algorithmTitle")}
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <h3 className="text-xl font-bold text-white mb-4">
                {t("tiktok.algorithm1Title")}
              </h3>
              <p className="text-white/70">
                {t("tiktok.algorithm1Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <h3 className="text-xl font-bold text-white mb-4">
                {t("tiktok.algorithm2Title")}
              </h3>
              <p className="text-white/70">
                {t("tiktok.algorithm2Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <h3 className="text-xl font-bold text-white mb-4">
                {t("tiktok.algorithm3Title")}
              </h3>
              <p className="text-white/70">
                {t("tiktok.algorithm3Description")}
              </p>
            </div>
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <h3 className="text-xl font-bold text-white mb-4">
                {t("tiktok.algorithm4Title")}
              </h3>
              <p className="text-white/70">
                {t("tiktok.algorithm4Description")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("tiktok.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("tiktok.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Video className="mr-2 h-5 w-5" />
              {t("tiktok.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("tiktok.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Tiktok;
