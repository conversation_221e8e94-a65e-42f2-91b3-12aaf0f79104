import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { Bar<PERSON><PERSON>, Building2, Clock, Palette, Users, Zap } from "lucide-react";

const MarketingAgencies = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Clock,
      title: t("marketingAgencies.feature1Title"),
      description: t("marketingAgencies.feature1Description"),
    },
    {
      icon: Palette,
      title: t("marketingAgencies.feature2Title"),
      description: t("marketingAgencies.feature2Description"),
    },
    {
      icon: Users,
      title: t("marketingAgencies.feature3Title"),
      description: t("marketingAgencies.feature3Description"),
    },
    {
      icon: <PERSON><PERSON><PERSON>,
      title: t("marketingAgencies.feature4Title"),
      description: t("marketingAgencies.feature4Description"),
    },
  ];

  const benefits = [
    t("marketingAgencies.benefit1"),
    t("marketingAgencies.benefit2"),
    t("marketingAgencies.benefit3"),
    t("marketingAgencies.benefit4"),
    t("marketingAgencies.benefit5"),
    t("marketingAgencies.benefit6"),
  ];

  const useCases = [
    {
      title: t("marketingAgencies.useCase1Title"),
      description: t("marketingAgencies.useCase1Description"),
    },
    {
      title: t("marketingAgencies.useCase2Title"),
      description: t("marketingAgencies.useCase2Description"),
    },
    {
      title: t("marketingAgencies.useCase3Title"),
      description: t("marketingAgencies.useCase3Description"),
    },
    {
      title: t("marketingAgencies.useCase4Title"),
      description: t("marketingAgencies.useCase4Description"),
    },
    {
      title: t("marketingAgencies.useCase5Title"),
      description: t("marketingAgencies.useCase5Description"),
    },
    {
      title: t("marketingAgencies.useCase6Title"),
      description: t("marketingAgencies.useCase6Description"),
    },
  ];

  const partnership = [
    {
      title: t("marketingAgencies.partnership1Title"),
      description: t("marketingAgencies.partnership1Description"),
    },
    {
      title: t("marketingAgencies.partnership2Title"),
      description: t("marketingAgencies.partnership2Description"),
    },
    {
      title: t("marketingAgencies.partnership3Title"),
      description: t("marketingAgencies.partnership3Description"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("marketingAgencies.meta.title")}
        description={t("marketingAgencies.meta.description")}
        keywords={t("marketingAgencies.meta.keywords")}
        url="https://miuta.ai/use-cases/marketing-agencies/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Marketing Agencies", url: "https://miuta.ai/use-cases/marketing-agencies/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Solutions for Marketing Agencies",
            "Scalable video production solutions for marketing agencies",
            ["Client Campaigns", "Social Media Content", "Ad Creatives"]
          ),
        ]}
      />
      <Header />

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("marketingAgencies.title")}
            <span className="text-gradient block mt-2">
              {t("marketingAgencies.subtitle")}
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("marketingAgencies.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Building2 className="mr-2 h-5 w-5" />
              {t("marketingAgencies.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("marketingAgencies.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("marketingAgencies.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("marketingAgencies.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("marketingAgencies.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Zap className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">
                {t("marketingAgencies.impactTitle")}
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("marketingAgencies.impact1Value")}
                  </div>
                  <div className="text-white/70">
                    {t("marketingAgencies.impact1Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("marketingAgencies.impact2Value")}
                  </div>
                  <div className="text-white/70">
                    {t("marketingAgencies.impact2Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("marketingAgencies.impact3Value")}
                  </div>
                  <div className="text-white/70">
                    {t("marketingAgencies.impact3Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("marketingAgencies.impact4Value")}
                  </div>
                  <div className="text-white/70">
                    {t("marketingAgencies.impact4Label")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("marketingAgencies.useCasesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10"
              >
                <h3 className="text-xl font-bold text-white mb-4">
                  {useCase.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {useCase.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("marketingAgencies.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">M</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">
                  {t("marketingAgencies.testimonialAuthor")}
                </div>
                <div className="text-white/60">
                  {t("marketingAgencies.testimonialTitle")}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Agency Pricing Preview */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("marketingAgencies.partnershipTitle")}
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {partnership.map((item, idx) => (
              <div
                key={idx}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10 text-center"
              >
                <h3 className="text-2xl font-bold text-white mb-4">
                  {item.title}
                </h3>
                <p className="text-white/70">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("marketingAgencies.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("marketingAgencies.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Building2 className="mr-2 h-5 w-5" />
              {t("marketingAgencies.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("marketingAgencies.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default MarketingAgencies;
