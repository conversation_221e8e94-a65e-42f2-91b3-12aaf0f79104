import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { Activity, Heart, Shield, Target, Users } from "lucide-react";

const HealthFitness = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Activity,
      title: t("healthFitness.feature1Title"),
      description: t("healthFitness.feature1Description"),
    },
    {
      icon: Heart,
      title: t("healthFitness.feature2Title"),
      description: t("healthFitness.feature2Description"),
    },
    {
      icon: Users,
      title: t("healthFitness.feature3Title"),
      description: t("healthFitness.feature3Description"),
    },
    {
      icon: Target,
      title: t("healthFitness.feature4Title"),
      description: t("healthFitness.feature4Description"),
    },
  ];

  const benefits = [
    t("healthFitness.benefit1"),
    t("healthFitness.benefit2"),
    t("healthFitness.benefit3"),
    t("healthFitness.benefit4"),
    t("healthFitness.benefit5"),
    t("healthFitness.benefit6"),
  ];

  const useCases = [
    {
      title: t("healthFitness.useCase1Title"),
      description: t("healthFitness.useCase1Description"),
    },
    {
      title: t("healthFitness.useCase2Title"),
      description: t("healthFitness.useCase2Description"),
    },
    {
      title: t("healthFitness.useCase3Title"),
      description: t("healthFitness.useCase3Description"),
    },
    {
      title: t("healthFitness.useCase4Title"),
      description: t("healthFitness.useCase4Description"),
    },
    {
      title: t("healthFitness.useCase5Title"),
      description: t("healthFitness.useCase5Description"),
    },
    {
      title: t("healthFitness.useCase6Title"),
      description: t("healthFitness.useCase6Description"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("healthFitness.meta.title")}
        description={t("healthFitness.meta.description")}
        keywords={t("healthFitness.meta.keywords")}
        url="https://miuta.ai/use-cases/health-fitness/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Health & Fitness", url: "https://miuta.ai/use-cases/health-fitness/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for Health & Fitness",
            "Professional video marketing solutions for health and fitness brands",
            ["Workout Demos", "Testimonials", "Service Showcases"]
          ),
        ]}
      />
      <Header />

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("healthFitness.title")}
            <span className="text-gradient block mt-2">
              {t("healthFitness.subtitle")}
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("healthFitness.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Heart className="mr-2 h-5 w-5" />
              {t("healthFitness.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("healthFitness.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("healthFitness.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("healthFitness.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("healthFitness.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Shield className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">
                {t("healthFitness.impactTitle")}
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("healthFitness.impact1Value")}
                  </div>
                  <div className="text-white/70">
                    {t("healthFitness.impact1Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("healthFitness.impact2Value")}
                  </div>
                  <div className="text-white/70">
                    {t("healthFitness.impact2Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("healthFitness.impact3Value")}
                  </div>
                  <div className="text-white/70">
                    {t("healthFitness.impact3Label")}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("healthFitness.impact4Value")}
                  </div>
                  <div className="text-white/70">
                    {t("healthFitness.impact4Label")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("healthFitness.useCasesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10"
              >
                <h3 className="text-xl font-bold text-white mb-4">
                  {useCase.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {useCase.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("healthFitness.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">D</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">
                  {t("healthFitness.testimonialAuthor")}
                </div>
                <div className="text-white/60">
                  {t("healthFitness.testimonialTitle")}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("healthFitness.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("healthFitness.ctaDescription")}
          </p>
          <Button size="lg" className="text-lg px-8 py-6">
            <Heart className="mr-2 h-5 w-5" />
            {t("healthFitness.ctaButton")}
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HealthFitness;
