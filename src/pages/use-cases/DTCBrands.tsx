import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { Crown, Heart, Package, Play, ShoppingCart, Star, TrendingUp, Users, Zap } from "lucide-react";

const DTCBrands = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Crown,
      title: t("dtcBrands.feature1Title"),
      description: t("dtcBrands.feature1Description")
    },
    {
      icon: Heart,
      title: t("dtcBrands.feature2Title"),
      description: t("dtcBrands.feature2Description")
    },
    {
      icon: Users,
      title: t("dtcBrands.feature3Title"),
      description: t("dtcBrands.feature3Description")
    },
    {
      icon: Star,
      title: t("dtcBrands.feature4Title"),
      description: t("dtcBrands.feature4Description")
    }
  ];

  const benefits = [
    t("dtcBrands.benefit1"),
    t("dtcBrands.benefit2"),
    t("dtcBrands.benefit3"),
    t("dtcBrands.benefit4"),
    t("dtcBrands.benefit5"),
    t("dtcBrands.benefit6")
  ];

  const strategies = [
    {
      title: t("dtcBrands.strategy1Title"),
      description: t("dtcBrands.strategy1Description")
    },
    {
      title: t("dtcBrands.strategy2Title"),
      description: t("dtcBrands.strategy2Description")
    },
    {
      title: t("dtcBrands.strategy3Title"),
      description: t("dtcBrands.strategy3Description")
    },
    {
      title: t("dtcBrands.strategy4Title"),
      description: t("dtcBrands.strategy4Description")
    },
    {
      title: t("dtcBrands.strategy5Title"),
      description: t("dtcBrands.strategy5Description")
    },
    {
      title: t("dtcBrands.strategy6Title"),
      description: t("dtcBrands.strategy6Description")
    }
  ];

  const contentTypes = [
    {
      title: t("dtcBrands.contentType1Title"),
      description: t("dtcBrands.contentType1Description")
    },
    {
      title: t("dtcBrands.contentType2Title"),
      description: t("dtcBrands.contentType2Description")
    },
    {
      title: t("dtcBrands.contentType3Title"),
      description: t("dtcBrands.contentType3Description")
    },
    {
      title: t("dtcBrands.contentType4Title"),
      description: t("dtcBrands.contentType4Description")
    }
  ];

  const framework = [
    {
      step: "1",
      title: t("dtcBrands.framework1Title"),
      description: t("dtcBrands.framework1Description")
    },
    {
      step: "2",
      title: t("dtcBrands.framework2Title"),
      description: t("dtcBrands.framework2Description")
    },
    {
      step: "3",
      title: t("dtcBrands.framework3Title"),
      description: t("dtcBrands.framework3Description")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("dtcBrands.meta.title")}
        description={t("dtcBrands.meta.description")}
        keywords={t("dtcBrands.meta.keywords")}
        url="https://miuta.ai/use-cases/dtc-brands/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "DTC Brands", url: "https://miuta.ai/use-cases/dtc-brands/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for DTC Brands",
            "Professional video marketing solutions for direct-to-consumer brands",
            ["Product Showcases", "Brand Stories", "Customer Testimonials"]
          ),
        ]}
      />
      <Header />
      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("dtcBrands.title")}
            <span className="text-gradient block mt-2">{t("dtcBrands.subtitle")}</span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("dtcBrands.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Crown className="mr-2 h-5 w-5" />
              {t("dtcBrands.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("dtcBrands.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("dtcBrands.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("dtcBrands.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("dtcBrands.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Zap className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">{t("dtcBrands.impactTitle")}</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("dtcBrands.impact1Value")}</div>
                  <div className="text-white/70">{t("dtcBrands.impact1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("dtcBrands.impact2Value")}</div>
                  <div className="text-white/70">{t("dtcBrands.impact2Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("dtcBrands.impact3Value")}</div>
                  <div className="text-white/70">{t("dtcBrands.impact3Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("dtcBrands.impact4Value")}</div>
                  <div className="text-white/70">{t("dtcBrands.impact4Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Strategies Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("dtcBrands.strategiesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {strategies.map((strategy, index) => (
              <div key={index} className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
                <h3 className="text-xl font-bold text-white mb-4">{strategy.title}</h3>
                <p className="text-white/70 leading-relaxed">{strategy.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Story */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("dtcBrands.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">A</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">{t("dtcBrands.testimonialAuthor")}</div>
                <div className="text-white/60">{t("dtcBrands.testimonialTitle")}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Calendar */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("dtcBrands.contentTypesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {contentTypes.map((item, idx) => (
              <div key={idx} className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
                <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                  <Play className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-white/70 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Brand Building Framework */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            {t("dtcBrands.frameworkTitle")}
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {framework.map((item, idx) => (
              <div key={idx} className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
                <div className="text-3xl font-bold text-gradient mb-4">{item.step}</div>
                <h3 className="text-xl font-bold text-white mb-4">{item.title}</h3>
                <p className="text-white/70">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("dtcBrands.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("dtcBrands.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Crown className="mr-2 h-5 w-5" />
              {t("dtcBrands.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("dtcBrands.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default DTCBrands;
