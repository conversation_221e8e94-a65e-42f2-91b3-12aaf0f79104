import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Trans, useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { DollarSign, Globe, Lightbulb, Rocket, TrendingUp, Users } from "lucide-react";

const Startups = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Lightbulb,
      title: t("startups.feature1Title"),
      description: t("startups.feature1Description"),
    },
    {
      icon: Users,
      title: t("startups.feature2Title"),
      description: t("startups.feature2Description"),
    },
    {
      icon: TrendingUp,
      title: t("startups.feature3Title"),
      description: t("startups.feature3Description"),
    },
    {
      icon: Globe,
      title: t("startups.feature4Title"),
      description: t("startups.feature4Description"),
    },
  ];

  const benefits = [
    t("startups.benefit1"),
    t("startups.benefit2"),
    t("startups.benefit3"),
    t("startups.benefit4"),
    t("startups.benefit5"),
    t("startups.benefit6"),
  ];

  const useCases = [
    {
      title: t("startups.useCase1Title"),
      description: t("startups.useCase1Description"),
    },
    {
      title: t("startups.useCase2Title"),
      description: t("startups.useCase2Description"),
    },
    {
      title: t("startups.useCase3Title"),
      description: t("startups.useCase3Description"),
    },
    {
      title: t("startups.useCase4Title"),
      description: t("startups.useCase4Description"),
    },
    {
      title: t("startups.useCase5Title"),
      description: t("startups.useCase5Description"),
    },
    {
      title: t("startups.useCase6Title"),
      description: t("startups.useCase6Description"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("startups.meta.title")}
        description={t("startups.meta.description")}
        keywords={t("startups.meta.keywords")}
        url="https://miuta.ai/use-cases/startups/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Startups", url: "https://miuta.ai/use-cases/startups/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for Startups",
            "Professional video marketing solutions for startups and entrepreneurs",
            ["Pitch Videos", "Product Demos", "Founder Stories"]
          ),
        ]}
      />
      <Header />

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("startups.title")}
            <span className="text-gradient block mt-2">
              {t("startups.subtitle")}
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            <Trans i18nKey="startups.description">
              Bootstrap your video marketing with{" "}
              <a
                href="/features/ai-avatars"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                professional AI actors
              </a>
              . Create compelling product demos, pitch videos, and marketing
              content without the enterprise budget. Perfect for{" "}
              <a
                href="/use-cases/ecommerce"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                e-commerce platforms
              </a>{" "}
              and working with{" "}
              <a
                href="/use-cases/marketing-agencies"
                className="text-gradient hover:text-primary transition-colors underline decoration-primary/50"
              >
                marketing agencies
              </a>
              .
            </Trans>
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Rocket className="mr-2 h-5 w-5" />
              {t("startups.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("startups.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("startups.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("startups.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("startups.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <DollarSign className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">
                {t("startups.roiTitle")}
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("startups.roi1Value")}
                  </div>
                  <div className="text-white/70">{t("startups.roi1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("startups.roi2Value")}
                  </div>
                  <div className="text-white/70">{t("startups.roi2Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("startups.roi3Value")}
                  </div>
                  <div className="text-white/70">{t("startups.roi3Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">
                    {t("startups.roi4Value")}
                  </div>
                  <div className="text-white/70">{t("startups.roi4Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("startups.useCasesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10"
              >
                <h3 className="text-xl font-bold text-white mb-4">
                  {useCase.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {useCase.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("startups.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">S</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">
                  {t("startups.testimonialAuthor")}
                </div>
                <div className="text-white/60">
                  {t("startups.testimonialTitle")}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("startups.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("startups.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Rocket className="mr-2 h-5 w-5" />
              {t("startups.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("startups.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Startups;
