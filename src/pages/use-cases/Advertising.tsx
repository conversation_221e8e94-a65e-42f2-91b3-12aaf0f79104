import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Target, TrendingUp, Zap, Eye, BarChart, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";

const Advertising = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Target,
      title: t("advertising.feature1Title"),
      description: t("advertising.feature1Description")
    },
    {
      icon: Eye,
      title: t("advertising.feature2Title"),
      description: t("advertising.feature2Description")
    },
    {
      icon: Zap,
      title: t("advertising.feature3Title"),
      description: t("advertising.feature3Description")
    },
    {
      icon: Users,
      title: t("advertising.feature4Title"),
      description: t("advertising.feature4Description")
    }
  ];

  const benefits = [
    t("advertising.benefit1"),
    t("advertising.benefit2"),
    t("advertising.benefit3"),
    t("advertising.benefit4"),
    t("advertising.benefit5"),
    t("advertising.benefit6")
  ];

  const platforms = [
    {
      name: t("advertising.platform1Name"),
      description: t("advertising.platform1Description")
    },
    {
      name: t("advertising.platform2Name"),
      description: t("advertising.platform2Description")
    },
    {
      name: t("advertising.platform3Name"),
      description: t("advertising.platform3Description")
    },
    {
      name: t("advertising.platform4Name"),
      description: t("advertising.platform4Description")
    },
    {
      name: t("advertising.platform5Name"),
      description: t("advertising.platform5Description")
    },
    {
      name: t("advertising.platform6Name"),
      description: t("advertising.platform6Description")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("advertising.meta.title")}
        description={t("advertising.meta.description")}
        keywords={t("advertising.meta.keywords")}
        url="https://miuta.ai/use-cases/advertising/"
        image="https://lovable.dev/opengraph-image-p98pqg.png"
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Advertising", url: "https://miuta.ai/use-cases/advertising/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Advertising Solutions",
            "Professional video advertising solutions with AI technology",
            ["Commercial Videos", "Social Ads", "Display Advertising"]
          ),
        ]}
      />
      <Header />
      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("advertising.title")}
            <span className="text-gradient block mt-2">{t("advertising.subtitle")}</span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("advertising.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Target className="mr-2 h-5 w-5" />
              {t("advertising.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("advertising.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("advertising.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Performance Stats */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("advertising.resultsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("advertising.resultsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <BarChart className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">{t("advertising.metricsTitle")}</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("advertising.metric1Value")}</div>
                  <div className="text-white/70">{t("advertising.metric1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("advertising.metric2Value")}</div>
                  <div className="text-white/70">{t("advertising.metric2Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("advertising.metric3Value")}</div>
                  <div className="text-white/70">{t("advertising.metric3Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("advertising.metric4Value")}</div>
                  <div className="text-white/70">{t("advertising.metric4Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Support */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("advertising.platformsTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {platforms.map((platform, index) => (
              <div key={index} className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
                <h3 className="text-xl font-bold text-white mb-4">{platform.name}</h3>
                <p className="text-white/70 leading-relaxed">{platform.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-6">{t("advertising.caseStudyTitle")}</h3>
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("advertising.caseStudyQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">R</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">{t("advertising.caseStudyAuthor")}</div>
                <div className="text-white/60">{t("advertising.caseStudyTitle2")}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Ad Examples Preview */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("advertising.examplesTitle")}
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("advertising.example1Title")}</h3>
              <p className="text-white/70 text-sm">{t("advertising.example1Description")}</p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("advertising.example2Title")}</h3>
              <p className="text-white/70 text-sm">{t("advertising.example2Description")}</p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("advertising.example3Title")}</h3>
              <p className="text-white/70 text-sm">{t("advertising.example3Description")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("advertising.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("advertising.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <Target className="mr-2 h-5 w-5" />
              {t("advertising.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("advertising.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Advertising;
