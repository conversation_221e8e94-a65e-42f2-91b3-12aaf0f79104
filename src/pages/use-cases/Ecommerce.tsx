import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";
import { BarChart, Package, Play, ShoppingCart, Star, TrendingUp, Users, Zap } from "lucide-react";

const Ecommerce = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: Package,
      title: t("ecommerce.feature1Title"),
      description: t("ecommerce.feature1Description")
    },
    {
      icon: Star,
      title: t("ecommerce.feature2Title"),
      description: t("ecommerce.feature2Description")
    },
    {
      icon: ShoppingCart,
      title: t("ecommerce.feature3Title"),
      description: t("ecommerce.feature3Description")
    },
    {
      icon: TrendingUp,
      title: t("ecommerce.feature4Title"),
      description: t("ecommerce.feature4Description")
    }
  ];

  const benefits = [
    t("ecommerce.benefit1"),
    t("ecommerce.benefit2"),
    t("ecommerce.benefit3"),
    t("ecommerce.benefit4"),
    t("ecommerce.benefit5"),
    t("ecommerce.benefit6")
  ];

  const useCases = [
    {
      title: t("ecommerce.useCase1Title"),
      description: t("ecommerce.useCase1Description")
    },
    {
      title: t("ecommerce.useCase2Title"),
      description: t("ecommerce.useCase2Description")
    },
    {
      title: t("ecommerce.useCase3Title"),
      description: t("ecommerce.useCase3Description")
    },
    {
      title: t("ecommerce.useCase4Title"),
      description: t("ecommerce.useCase4Description")
    },
    {
      title: t("ecommerce.useCase5Title"),
      description: t("ecommerce.useCase5Description")
    },
    {
      title: t("ecommerce.useCase6Title"),
      description: t("ecommerce.useCase6Description")
    }
  ];

  const videoTypes = [
    {
      title: t("ecommerce.videoType1Title"),
      description: t("ecommerce.videoType1Description")
    },
    {
      title: t("ecommerce.videoType2Title"),
      description: t("ecommerce.videoType2Description")
    },
    {
      title: t("ecommerce.videoType3Title"),
      description: t("ecommerce.videoType3Description")
    },
    {
      title: t("ecommerce.videoType4Title"),
      description: t("ecommerce.videoType4Description")
    }
  ];

  const platforms = [
    t("ecommerce.platform1"),
    t("ecommerce.platform2"),
    t("ecommerce.platform3"),
    t("ecommerce.platform4")
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("ecommerce.meta.title")}
        description={t("ecommerce.meta.description")}
        keywords={t("ecommerce.meta.keywords")}
        url="https://miuta.ai/use-cases/ecommerce/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "E-commerce", url: "https://miuta.ai/use-cases/ecommerce/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for E-commerce",
            "Professional video marketing solutions for e-commerce businesses",
            ["Product Demos", "Unboxing Videos", "Customer Reviews"]
          ),
        ]}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("ecommerce.title")}
            <span className="text-gradient block mt-2">{t("ecommerce.subtitle")}</span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("ecommerce.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <ShoppingCart className="mr-2 h-5 w-5" />
              {t("ecommerce.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("ecommerce.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("ecommerce.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("ecommerce.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("ecommerce.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Zap className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">{t("ecommerce.resultsTitle")}</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("ecommerce.result1Value")}</div>
                  <div className="text-white/70">{t("ecommerce.result1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("ecommerce.result2Value")}</div>
                  <div className="text-white/70">{t("ecommerce.result2Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("ecommerce.useCasesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div key={index} className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
                <h3 className="text-xl font-bold text-white mb-4">{useCase.title}</h3>
                <p className="text-white/70 leading-relaxed">{useCase.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Story */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("ecommerce.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">E</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">{t("ecommerce.testimonialAuthor")}</div>
                <div className="text-white/60">{t("ecommerce.testimonialTitle")}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Video Types */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("ecommerce.videoTypesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {videoTypes.map((item, idx) => (
              <div key={idx} className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
                <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                  <Play className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{item.title}</h3>
                <p className="text-white/70 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Integration Benefits */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            {t("ecommerce.integrationTitle")}
          </h2>
          <p className="text-white/70 mb-12 leading-relaxed">
            {t("ecommerce.integrationDescription")}
          </p>
          <div className="grid md:grid-cols-4 gap-6">
            {platforms.map((platform, idx) => (
              <div key={platform} className="bg-gradient-card p-6 rounded-xl backdrop-blur-sm border border-white/10">
                <h3 className="text-white font-semibold">{platform}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("ecommerce.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("ecommerce.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <ShoppingCart className="mr-2 h-5 w-5" />
              {t("ecommerce.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("ecommerce.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Ecommerce;
