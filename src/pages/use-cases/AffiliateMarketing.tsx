import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play, DollarSign, TrendingUp, Users, Link, BarChart, Zap } from "lucide-react";
import { useTranslation } from "react-i18next";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema, generateUseCaseServiceSchema } from "@/lib/schema";

const AffiliateMarketing = () => {
  const { t } = useTranslation('use-cases');

  const features = [
    {
      icon: DollarSign,
      title: t("affiliateMarketing.feature1Title"),
      description: t("affiliateMarketing.feature1Description")
    },
    {
      icon: Link,
      title: t("affiliateMarketing.feature2Title"),
      description: t("affiliateMarketing.feature2Description")
    },
    {
      icon: TrendingUp,
      title: t("affiliateMarketing.feature3Title"),
      description: t("affiliateMarketing.feature3Description")
    },
    {
      icon: Users,
      title: t("affiliateMarketing.feature4Title"),
      description: t("affiliateMarketing.feature4Description")
    }
  ];

  const benefits = [
    t("affiliateMarketing.benefit1"),
    t("affiliateMarketing.benefit2"),
    t("affiliateMarketing.benefit3"),
    t("affiliateMarketing.benefit4"),
    t("affiliateMarketing.benefit5"),
    t("affiliateMarketing.benefit6")
  ];

  const strategies = [
    {
      title: t("affiliateMarketing.strategy1Title"),
      description: t("affiliateMarketing.strategy1Description")
    },
    {
      title: t("affiliateMarketing.strategy2Title"),
      description: t("affiliateMarketing.strategy2Description")
    },
    {
      title: t("affiliateMarketing.strategy3Title"),
      description: t("affiliateMarketing.strategy3Description")
    },
    {
      title: t("affiliateMarketing.strategy4Title"),
      description: t("affiliateMarketing.strategy4Description")
    },
    {
      title: t("affiliateMarketing.strategy5Title"),
      description: t("affiliateMarketing.strategy5Description")
    },
    {
      title: t("affiliateMarketing.strategy6Title"),
      description: t("affiliateMarketing.strategy6Description")
    }
  ];

  const platforms = [
    t("affiliateMarketing.platform1"),
    t("affiliateMarketing.platform2"),
    t("affiliateMarketing.platform3"),
    t("affiliateMarketing.platform4")
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("affiliateMarketing.meta.title")}
        description={t("affiliateMarketing.meta.description")}
        keywords={t("affiliateMarketing.meta.keywords")}
        url="https://miuta.ai/use-cases/affiliate-marketing/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          generateBreadcrumbSchema([
            { name: "Home", url: "https://miuta.ai/" },
            { name: "Use Cases", url: "https://miuta.ai/use-cases/" },
            { name: "Affiliate Marketing", url: "https://miuta.ai/use-cases/affiliate-marketing/" },
          ]),
          generateUseCaseServiceSchema(
            "AI Video Marketing for Affiliate Marketing",
            "Professional video marketing solutions for affiliate marketers",
            ["Product Reviews", "Promotional Videos", "Testimonials"]
          ),
        ]}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            {t("affiliateMarketing.title")}
            <span className="text-gradient block mt-2">{t("affiliateMarketing.subtitle")}</span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            {t("affiliateMarketing.description")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <DollarSign className="mr-2 h-5 w-5" />
              {t("affiliateMarketing.heroButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("affiliateMarketing.heroButton2")}
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("affiliateMarketing.featuresTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 rounded-full gradient-button mx-auto mb-6 flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">
              {t("affiliateMarketing.benefitsTitle")}
            </h2>
            <p className="text-white/70 mb-8 leading-relaxed">
              {t("affiliateMarketing.benefitsDescription")}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-gradient-button mr-4"></div>
                  <span className="text-white/80">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
              <Zap className="w-12 h-12 text-white mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">{t("affiliateMarketing.performanceTitle")}</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("affiliateMarketing.performance1Value")}</div>
                  <div className="text-white/70">{t("affiliateMarketing.performance1Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("affiliateMarketing.performance2Value")}</div>
                  <div className="text-white/70">{t("affiliateMarketing.performance2Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("affiliateMarketing.performance3Value")}</div>
                  <div className="text-white/70">{t("affiliateMarketing.performance3Label")}</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-gradient">{t("affiliateMarketing.performance4Value")}</div>
                  <div className="text-white/70">{t("affiliateMarketing.performance4Label")}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Strategies Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("affiliateMarketing.strategiesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {strategies.map((strategy, index) => (
              <div key={index} className="bg-gradient-card p-8 rounded-2xl backdrop-blur-sm border border-white/10">
                <h3 className="text-xl font-bold text-white mb-4">{strategy.title}</h3>
                <p className="text-white/70 leading-relaxed">{strategy.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Story */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-card p-12 rounded-2xl backdrop-blur-sm border border-white/10">
            <p className="text-xl text-white/90 mb-8 italic leading-relaxed">
              "{t("affiliateMarketing.testimonialQuote")}"
            </p>
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-full gradient-button flex items-center justify-center mr-4">
                <span className="text-white font-bold">T</span>
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">{t("affiliateMarketing.testimonialAuthor")}</div>
                <div className="text-white/60">{t("affiliateMarketing.testimonialTitle")}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Examples */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            {t("affiliateMarketing.examplesTitle")}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("affiliateMarketing.example1Title")}</h3>
              <p className="text-white/70 text-sm">{t("affiliateMarketing.example1Description")}</p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("affiliateMarketing.example2Title")}</h3>
              <p className="text-white/70 text-sm">{t("affiliateMarketing.example2Description")}</p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("affiliateMarketing.example3Title")}</h3>
              <p className="text-white/70 text-sm">{t("affiliateMarketing.example3Description")}</p>
            </div>
            <div className="bg-gradient-card p-6 rounded-2xl backdrop-blur-sm border border-white/10">
              <div className="aspect-video bg-white/10 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">{t("affiliateMarketing.example4Title")}</h3>
              <p className="text-white/70 text-sm">{t("affiliateMarketing.example4Description")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Integration */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            {t("affiliateMarketing.platformsTitle")}
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            {platforms.map((platform, idx) => (
              <div key={platform} className="bg-gradient-card p-6 rounded-xl backdrop-blur-sm border border-white/10">
                <h3 className="text-white font-semibold">{platform}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t("affiliateMarketing.ctaTitle")}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t("affiliateMarketing.ctaDescription")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6">
              <DollarSign className="mr-2 h-5 w-5" />
              {t("affiliateMarketing.ctaButton1")}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              {t("affiliateMarketing.ctaButton2")}
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default AffiliateMarketing;
