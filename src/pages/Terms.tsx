import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { HeadContent } from "@/components/HeadContent";
import { generateBreadcrumbSchema } from "@/lib/schema";
import { useTranslation } from "react-i18next";

const Terms = () => {
  const { t } = useTranslation("terms");
  
  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("headContent.title")}
        description={t("headContent.description")}
        keywords={t("headContent.keywords")}
        url="https://miuta.ai/terms/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[generateBreadcrumbSchema([
          { name: "Home", url: "https://miuta.ai/" },
          { name: "Terms of Service", url: "https://miuta.ai/terms/" },
        ])]}
      />
      
      <Header />
      
      <main className="container mx-auto px-6 py-24">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">{t('terms.title', 'Terms & Conditions')}</h1>
          <p className="text-white/60 mb-12">{t('terms.lastUpdated', 'Last updated: December 2024')}</p>
          
          <div className="prose prose-invert max-w-none">
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section1.title', '1. Company Information')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section1.content', 'Miuta is an AI-powered video creation platform that provides services for generating marketing videos with realistic AI actors. Our platform is accessible at miuta.ai and through our API services.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section2.title', '2. Services Offered')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section2.intro', 'Miuta offers AI-powered video creation services including:')}
              </p>
              <ul className="list-disc list-inside text-white/80 space-y-2 mb-4">
                <li>{t('terms.section2.service1', 'AI actor video generation with realistic human avatars')}</li>
                <li>{t('terms.section2.service2', 'Automated script writing and optimization')}</li>
                <li>{t('terms.section2.service3', 'Multi-language voice synthesis and dubbing')}</li>
                <li>{t('terms.section2.service4', 'Brand customization and template creation')}</li>
                <li>{t('terms.section2.service5', 'API access for enterprise integrations')}</li>
                <li>{t('terms.section2.service6', 'Analytics and performance tracking')}</li>
              </ul>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section3.title', '3. Acceptance of Terms')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section3.content', 'By accessing or using our services, you agree to be bound by these Terms & Conditions. If you do not agree to all terms, you may not access or use our services.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section4.title', '4. User Accounts and Registration')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section4.intro', 'To access certain features, you must create an account. You are responsible for:')}
              </p>
              <ul className="list-disc list-inside text-white/80 space-y-2 mb-4">
                <li>{t('terms.section4.item1', 'Providing accurate and complete registration information')}</li>
                <li>{t('terms.section4.item2', 'Maintaining the security of your account credentials')}</li>
                <li>{t('terms.section4.item3', 'All activities that occur under your account')}</li>
                <li>{t('terms.section4.item4', 'Notifying us immediately of any unauthorized use')}</li>
              </ul>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section5.title', '5. Permitted Use')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section5.intro', 'You may use our services for legitimate business and personal purposes. You agree not to:')}
              </p>
              <ul className="list-disc list-inside text-white/80 space-y-2 mb-4">
                <li>{t('terms.section5.item1', 'Create content that violates laws or regulations')}</li>
                <li>{t('terms.section5.item2', 'Generate deepfakes or misleading content without disclosure')}</li>
                <li>{t('terms.section5.item3', 'Infringe on intellectual property rights')}</li>
                <li>{t('terms.section5.item4', 'Use the service for spam or malicious activities')}</li>
                <li>{t('terms.section5.item5', 'Attempt to reverse engineer or hack our systems')}</li>
                <li>{t('terms.section5.item6', 'Share account credentials with unauthorized users')}</li>
              </ul>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section6.title', '6. Content and Intellectual Property')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section6.content', 'You retain ownership of content you create using our platform. However, you grant us a license to use, modify, and distribute your content as necessary to provide our services. All AI models, software, and platform technology remain our intellectual property.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section7.title', '7. Payment and Billing')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section7.content', 'Subscription fees are billed in advance and are non-refundable except as required by law. We may change pricing with 30 days notice. Failure to pay may result in service suspension or termination.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section8.title', '8. Service Availability')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section8.content', 'We strive for high availability but do not guarantee uninterrupted service. We may perform maintenance, updates, or modifications that temporarily affect service availability.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section9.title', '9. Limitation of Liability')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section9.content', 'To the maximum extent permitted by law, Miuta shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our services.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section10.title', '10. Termination')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section10.content', 'Either party may terminate the agreement at any time. Upon termination, your access to the service will cease, but these terms will continue to apply to any disputes or claims.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section11.title', '11. Changes to Terms')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section11.content', 'We may update these terms from time to time. We will notify users of material changes via email or platform notification. Continued use after changes constitutes acceptance.')}
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-white mb-4">{t('terms.section12.title', '12. Contact Information')}</h2>
              <p className="text-white/80 leading-relaxed mb-4">
                {t('terms.section12.content', 'For questions about these Terms & Conditions, please contact <NAME_EMAIL> or through our support portal.')}
              </p>
            </section>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Terms;
