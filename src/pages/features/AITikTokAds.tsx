import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Music, TrendingUp, Zap, Users, Heart, Video } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AITikTokAds = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: Music,
      title: t("aiTikTokAds.benefit1Title"),
      description: t("aiTikTokAds.benefit1Description"),
    },
    {
      icon: TrendingUp,
      title: t("aiTikTokAds.benefit2Title"),
      description: t("aiTikTokAds.benefit2Description"),
    },
    {
      icon: Zap,
      title: t("aiTikTokAds.benefit3Title"),
      description: t("aiTikTokAds.benefit3Description"),
    },
    {
      icon: Users,
      title: t("aiTikTokAds.benefit4Title"),
      description: t("aiTikTokAds.benefit4Description"),
    },
    {
      icon: Heart,
      title: t("aiTikTokAds.benefit5Title"),
      description: t("aiTikTokAds.benefit5Description"),
    },
    {
      icon: Video,
      title: t("aiTikTokAds.benefit6Title"),
      description: t("aiTikTokAds.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiTikTokAds.faq1Question"),
      answer: t("aiTikTokAds.faq1Answer"),
    },
    {
      question: t("aiTikTokAds.faq2Question"),
      answer: t("aiTikTokAds.faq2Answer"),
    },
    {
      question: t("aiTikTokAds.faq3Question"),
      answer: t("aiTikTokAds.faq3Answer"),
    },
    {
      question: t("aiTikTokAds.faq4Question"),
      answer: t("aiTikTokAds.faq4Answer"),
    },
    {
      question: t("aiTikTokAds.faq5Question"),
      answer: t("aiTikTokAds.faq5Answer"),
    },
    {
      question: t("aiTikTokAds.faq6Question"),
      answer: t("aiTikTokAds.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiTikTokAds.testimonial1Name"),
      role: t("aiTikTokAds.testimonial1Role"),
      company: t("aiTikTokAds.testimonial1Company"),
      content: t("aiTikTokAds.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiTikTokAds.testimonial2Name"),
      role: t("aiTikTokAds.testimonial2Role"),
      company: t("aiTikTokAds.testimonial2Company"),
      content: t("aiTikTokAds.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiTikTokAds.testimonial3Name"),
      role: t("aiTikTokAds.testimonial3Role"),
      company: t("aiTikTokAds.testimonial3Company"),
      content: t("aiTikTokAds.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiTikTokAds.meta.title")}
        description={t("aiTikTokAds.meta.description")}
        keywords={t("aiTikTokAds.meta.keywords")}
        url="https://miuta.ai/features/ai-tiktok-ads/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiTikTokAds.title")}
        subtitle={t("aiTikTokAds.subtitle")}
        description={t("aiTikTokAds.description")}
        ctaText={t("aiTikTokAds.ctaText")}
        demoText={t("aiTikTokAds.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiTikTokAds.benefitsTitle")}
        subtitle={t("aiTikTokAds.benefitsSubtitle")}
        benefits={benefits}
      />
      
      <FeatureTestimonials testimonials={testimonials} />
      
      <FeatureFAQ faqs={faqs} />
      
      <FeatureCTA
        title={t("aiTikTokAds.ctaTitle")}
        subtitle={t("aiTikTokAds.ctaSubtitle")}
        primaryCTA={t("aiTikTokAds.ctaPrimary")}
        secondaryCTA={t("aiTikTokAds.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AITikTokAds;
