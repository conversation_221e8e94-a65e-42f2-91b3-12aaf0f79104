import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";
import { Download, Gift, Sparkles, Users } from "lucide-react";

const FreeAITools = () => {
  const { t } = useTranslation("features");

  const faqData = [
    {
      question: t("freeAITools.faq1Question"),
      answer: t("freeAITools.faq1Answer")
    },
    {
      question: t("freeAITools.faq2Question"),
      answer: t("freeAITools.faq2Answer")
    },
    {
      question: t("freeAITools.faq3Question"),
      answer: t("freeAITools.faq3Answer")
    },
    {
      question: t("freeAITools.faq4Question"),
      answer: t("freeAITools.faq4Answer")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("freeAITools.meta.title")}
        description={t("freeAITools.meta.description")}
        keywords={t("freeAITools.meta.keywords")}
        url="https://miuta.ai/features/free-ai-tools/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
      />
      
      <Header />
      
      <FeatureHero
        title={t("freeAITools.title")}
        subtitle={t("freeAITools.subtitle")}
        description={t("freeAITools.description")}
        ctaText={t("freeAITools.ctaText")}
        demoText={t("freeAITools.demoText")}
      />
      
      <FeatureBenefits
        title={t("freeAITools.benefitsTitle")}
        subtitle={t("freeAITools.benefitsSubtitle")}
        benefits={[
          {
            icon: Gift,
            title: t("freeAITools.benefit1Title"),
            description: t("freeAITools.benefit1Description")
          },
          {
            icon: Sparkles,
            title: t("freeAITools.benefit2Title"),
            description: t("freeAITools.benefit2Description")
          },
          {
            icon: Download,
            title: t("freeAITools.benefit3Title"),
            description: t("freeAITools.benefit3Description")
          },
          {
            icon: Users,
            title: t("freeAITools.benefit4Title"),
            description: t("freeAITools.benefit4Description")
          }
        ]}
      />
      
      <FeatureTestimonials
        title={t("freeAITools.testimonialsTitle")}
        testimonials={[
          {
            content: t("freeAITools.testimonial1Content"),
            name: t("freeAITools.testimonial1Name"),
            role: t("freeAITools.testimonial1Role"),
            company: t("freeAITools.testimonial1Company"),
            rating: 5
          },
          {
            content: t("freeAITools.testimonial2Content"),
            name: t("freeAITools.testimonial2Name"),
            role: t("freeAITools.testimonial2Role"),
            company: t("freeAITools.testimonial2Company"),
            rating: 5
          },
          {
            content: t("freeAITools.testimonial3Content"),
            name: t("freeAITools.testimonial3Name"),
            role: t("freeAITools.testimonial3Role"),
            company: t("freeAITools.testimonial3Company"),
            rating: 5
          }
        ]}
      />
      
      <FeatureFAQ
        title={t("freeAITools.faqTitle")}
        faqs={faqData}
      />
      
      <FeatureCTA
        title={t("freeAITools.ctaTitle")}
        subtitle={t("freeAITools.ctaSubtitle")}
        primaryCTA={t("freeAITools.ctaPrimary")}
        secondaryCTA={t("freeAITools.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default FreeAITools;
