import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Video, Scissors, Zap, Clock, Sparkles, RefreshCw } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIVideoEditor = () => {
  const { t } = useTranslation("features");

  const faqData = [
    {
      question: t("aiVideoEditor.faq1Question"),
      answer: t("aiVideoEditor.faq1Answer")
    },
    {
      question: t("aiVideoEditor.faq2Question"),
      answer: t("aiVideoEditor.faq2Answer")
    },
    {
      question: t("aiVideoEditor.faq3Question"),
      answer: t("aiVideoEditor.faq3Answer")
    },
    {
      question: t("aiVideoEditor.faq4Question"),
      answer: t("aiVideoEditor.faq4Answer")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiVideoEditor.meta.title")}
        description={t("aiVideoEditor.meta.description")}
        keywords={t("aiVideoEditor.meta.keywords")}
        url="https://miuta.ai/features/ai-video-editor/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiVideoEditor.title")}
        subtitle={t("aiVideoEditor.subtitle")}
        description={t("aiVideoEditor.description")}
        ctaText={t("aiVideoEditor.ctaText")}
        demoText={t("aiVideoEditor.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiVideoEditor.benefitsTitle")}
        subtitle={t("aiVideoEditor.benefitsSubtitle")}
        benefits={[
          {
            icon: Scissors,
            title: t("aiVideoEditor.benefit1Title"),
            description: t("aiVideoEditor.benefit1Description")
          },
          {
            icon: RefreshCw,
            title: t("aiVideoEditor.benefit2Title"),
            description: t("aiVideoEditor.benefit2Description")
          },
          {
            icon: Sparkles,
            title: t("aiVideoEditor.benefit3Title"),
            description: t("aiVideoEditor.benefit3Description")
          },
          {
            icon: Clock,
            title: t("aiVideoEditor.benefit4Title"),
            description: t("aiVideoEditor.benefit4Description")
          }
        ]}
      />
      
      <FeatureTestimonials
        title={t("aiVideoEditor.testimonialsTitle")}
        testimonials={[
          {
            content: t("aiVideoEditor.testimonial1Content"),
            name: t("aiVideoEditor.testimonial1Name"),
            role: t("aiVideoEditor.testimonial1Role"),
            company: t("aiVideoEditor.testimonial1Company"),
            rating: 5
          },
          {
            content: t("aiVideoEditor.testimonial2Content"),
            name: t("aiVideoEditor.testimonial2Name"),
            role: t("aiVideoEditor.testimonial2Role"),
            company: t("aiVideoEditor.testimonial2Company"),
            rating: 5
          },
          {
            content: t("aiVideoEditor.testimonial3Content"),
            name: t("aiVideoEditor.testimonial3Name"),
            role: t("aiVideoEditor.testimonial3Role"),
            company: t("aiVideoEditor.testimonial3Company"),
            rating: 5
          }
        ]}
      />
      
      <FeatureFAQ
        title={t("aiVideoEditor.faqTitle")}
        faqs={faqData}
      />
      
      <FeatureCTA
        title={t("aiVideoEditor.ctaTitle")}
        subtitle={t("aiVideoEditor.ctaSubtitle")}
        primaryCTA={t("aiVideoEditor.ctaPrimary")}
        secondaryCTA={t("aiVideoEditor.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AIVideoEditor;
