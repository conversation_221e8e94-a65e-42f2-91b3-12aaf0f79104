import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { FileText, Zap, Target, Brain, Clock, Sparkles } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIScriptGenerator = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: FileText,
      title: t("aiScriptGenerator.benefit1Title"),
      description: t("aiScriptGenerator.benefit1Description"),
    },
    {
      icon: Zap,
      title: t("aiScriptGenerator.benefit2Title"),
      description: t("aiScriptGenerator.benefit2Description"),
    },
    {
      icon: Target,
      title: t("aiScriptGenerator.benefit3Title"),
      description: t("aiScriptGenerator.benefit3Description"),
    },
    {
      icon: Brain,
      title: t("aiScriptGenerator.benefit4Title"),
      description: t("aiScriptGenerator.benefit4Description"),
    },
    {
      icon: Clock,
      title: t("aiScriptGenerator.benefit5Title"),
      description: t("aiScriptGenerator.benefit5Description"),
    },
    {
      icon: Sparkles,
      title: t("aiScriptGenerator.benefit6Title"),
      description: t("aiScriptGenerator.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiScriptGenerator.faq1Question"),
      answer: t("aiScriptGenerator.faq1Answer"),
    },
    {
      question: t("aiScriptGenerator.faq2Question"),
      answer: t("aiScriptGenerator.faq2Answer"),
    },
    {
      question: t("aiScriptGenerator.faq3Question"),
      answer: t("aiScriptGenerator.faq3Answer"),
    },
    {
      question: t("aiScriptGenerator.faq4Question"),
      answer: t("aiScriptGenerator.faq4Answer"),
    },
    {
      question: t("aiScriptGenerator.faq5Question"),
      answer: t("aiScriptGenerator.faq5Answer"),
    },
    {
      question: t("aiScriptGenerator.faq6Question"),
      answer: t("aiScriptGenerator.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiScriptGenerator.testimonial1Name"),
      role: t("aiScriptGenerator.testimonial1Role"),
      company: t("aiScriptGenerator.testimonial1Company"),
      content: t("aiScriptGenerator.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiScriptGenerator.testimonial2Name"),
      role: t("aiScriptGenerator.testimonial2Role"),
      company: t("aiScriptGenerator.testimonial2Company"),
      content: t("aiScriptGenerator.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiScriptGenerator.testimonial3Name"),
      role: t("aiScriptGenerator.testimonial3Role"),
      company: t("aiScriptGenerator.testimonial3Company"),
      content: t("aiScriptGenerator.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiScriptGenerator.meta.title")}
        description={t("aiScriptGenerator.meta.description")}
        keywords={t("aiScriptGenerator.meta.keywords")}
        url="https://miuta.ai/features/ai-script-generator/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiScriptGenerator.title")}
        subtitle={t("aiScriptGenerator.subtitle")}
        description={t("aiScriptGenerator.description")}
        ctaText={t("aiScriptGenerator.ctaText")}
        demoText={t("aiScriptGenerator.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiScriptGenerator.benefitsTitle")}
        subtitle={t("aiScriptGenerator.benefitsSubtitle")}
        benefits={benefits}
      />
      
      <FeatureTestimonials testimonials={testimonials} />
      
      <FeatureFAQ faqs={faqs} />
      
      <FeatureCTA
        title={t("aiScriptGenerator.ctaTitle")}
        subtitle={t("aiScriptGenerator.ctaSubtitle")}
        primaryCTA={t("aiScriptGenerator.ctaPrimary")}
        secondaryCTA={t("aiScriptGenerator.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AIScriptGenerator;
