import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";
import { Eye, Shield, TrendingUp, Zap } from "lucide-react";

const FacelessContentCreator = () => {
  const { t } = useTranslation("features");

  const faqData = [
    {
      question: t("facelessContentCreator.faq1Question"),
      answer: t("facelessContentCreator.faq1Answer")
    },
    {
      question: t("facelessContentCreator.faq2Question"),
      answer: t("facelessContentCreator.faq2Answer")
    },
    {
      question: t("facelessContentCreator.faq3Question"),
      answer: t("facelessContentCreator.faq3Answer")
    },
    {
      question: t("facelessContentCreator.faq4Question"),
      answer: t("facelessContentCreator.faq4Answer")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("facelessContentCreator.meta.title")}
        description={t("facelessContentCreator.meta.description")}
        keywords={t("facelessContentCreator.meta.keywords")}
        url="https://miuta.ai/features/faceless-content-creator/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
      />
      
      <Header />
      
      <FeatureHero
        title={t("facelessContentCreator.title")}
        subtitle={t("facelessContentCreator.subtitle")}
        description={t("facelessContentCreator.description")}
        ctaText={t("facelessContentCreator.ctaText")}
        demoText={t("facelessContentCreator.demoText")}
      />
      
      <FeatureBenefits
        title={t("facelessContentCreator.benefitsTitle")}
        subtitle={t("facelessContentCreator.benefitsSubtitle")}
        benefits={[
          {
            icon: Shield,
            title: t("facelessContentCreator.benefit1Title"),
            description: t("facelessContentCreator.benefit1Description")
          },
          {
            icon: Eye,
            title: t("facelessContentCreator.benefit2Title"),
            description: t("facelessContentCreator.benefit2Description")
          },
          {
            icon: TrendingUp,
            title: t("facelessContentCreator.benefit3Title"),
            description: t("facelessContentCreator.benefit3Description")
          },
          {
            icon: Zap,
            title: t("facelessContentCreator.benefit4Title"),
            description: t("facelessContentCreator.benefit4Description")
          }
        ]}
      />
      
      <FeatureTestimonials
        title={t("facelessContentCreator.testimonialsTitle")}
        testimonials={[
          {
            content: t("facelessContentCreator.testimonial1Content"),
            name: t("facelessContentCreator.testimonial1Name"),
            role: t("facelessContentCreator.testimonial1Role"),
            company: t("facelessContentCreator.testimonial1Company"),
            rating: 5
          },
          {
            content: t("facelessContentCreator.testimonial2Content"),
            name: t("facelessContentCreator.testimonial2Name"),
            role: t("facelessContentCreator.testimonial2Role"),
            company: t("facelessContentCreator.testimonial2Company"),
            rating: 5
          },
          {
            content: t("facelessContentCreator.testimonial3Content"),
            name: t("facelessContentCreator.testimonial3Name"),
            role: t("facelessContentCreator.testimonial3Role"),
            company: t("facelessContentCreator.testimonial3Company"),
            rating: 5
          }
        ]}
      />
      
      <FeatureFAQ
        title={t("facelessContentCreator.faqTitle")}
        faqs={faqData}
      />
      
      <FeatureCTA
        title={t("facelessContentCreator.ctaTitle")}
        subtitle={t("facelessContentCreator.ctaSubtitle")}
        primaryCTA={t("facelessContentCreator.ctaPrimary")}
        secondaryCTA={t("facelessContentCreator.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default FacelessContentCreator;
