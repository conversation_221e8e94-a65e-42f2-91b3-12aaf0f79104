import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Instagram, Camera, Heart, Zap, TrendingUp, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIInstagramAds = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: Camera,
      title: t("aiInstagramAds.benefit1Title"),
      description: t("aiInstagramAds.benefit1Description"),
    },
    {
      icon: Heart,
      title: t("aiInstagramAds.benefit2Title"),
      description: t("aiInstagramAds.benefit2Description"),
    },
    {
      icon: Zap,
      title: t("aiInstagramAds.benefit3Title"),
      description: t("aiInstagramAds.benefit3Description"),
    },
    {
      icon: TrendingUp,
      title: t("aiInstagramAds.benefit4Title"),
      description: t("aiInstagramAds.benefit4Description"),
    },
    {
      icon: Users,
      title: t("aiInstagramAds.benefit5Title"),
      description: t("aiInstagramAds.benefit5Description"),
    },
    {
      icon: Instagram,
      title: t("aiInstagramAds.benefit6Title"),
      description: t("aiInstagramAds.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiInstagramAds.faq1Question"),
      answer: t("aiInstagramAds.faq1Answer"),
    },
    {
      question: t("aiInstagramAds.faq2Question"),
      answer: t("aiInstagramAds.faq2Answer"),
    },
    {
      question: t("aiInstagramAds.faq3Question"),
      answer: t("aiInstagramAds.faq3Answer"),
    },
    {
      question: t("aiInstagramAds.faq4Question"),
      answer: t("aiInstagramAds.faq4Answer"),
    },
    {
      question: t("aiInstagramAds.faq5Question"),
      answer: t("aiInstagramAds.faq5Answer"),
    },
    {
      question: t("aiInstagramAds.faq6Question"),
      answer: t("aiInstagramAds.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiInstagramAds.testimonial1Name"),
      role: t("aiInstagramAds.testimonial1Role"),
      company: t("aiInstagramAds.testimonial1Company"),
      content: t("aiInstagramAds.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiInstagramAds.testimonial2Name"),
      role: t("aiInstagramAds.testimonial2Role"),
      company: t("aiInstagramAds.testimonial2Company"),
      content: t("aiInstagramAds.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiInstagramAds.testimonial3Name"),
      role: t("aiInstagramAds.testimonial3Role"),
      company: t("aiInstagramAds.testimonial3Company"),
      content: t("aiInstagramAds.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiInstagramAds.meta.title")}
        description={t("aiInstagramAds.meta.description")}
        keywords={t("aiInstagramAds.meta.keywords")}
        url="https://miuta.ai/features/ai-instagram-ads/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiInstagramAds.title")}
        subtitle={t("aiInstagramAds.subtitle")}
        description={t("aiInstagramAds.description")}
        ctaText={t("aiInstagramAds.ctaText")}
        demoText={t("aiInstagramAds.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiInstagramAds.benefitsTitle")}
        subtitle={t("aiInstagramAds.benefitsSubtitle")}
        benefits={benefits}
      />
      
      <FeatureTestimonials testimonials={testimonials} />
      
      <FeatureFAQ faqs={faqs} />
      
      <FeatureCTA
        title={t("aiInstagramAds.ctaTitle")}
        subtitle={t("aiInstagramAds.ctaSubtitle")}
        primaryCTA={t("aiInstagramAds.ctaPrimary")}
        secondaryCTA={t("aiInstagramAds.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AIInstagramAds;
