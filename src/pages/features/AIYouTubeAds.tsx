import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Youtube, Play, TrendingUp, Target, Users, Video } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIYouTubeAds = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: Youtube,
      title: t("aiYouTubeAds.benefit1Title"),
      description: t("aiYouTubeAds.benefit1Description"),
    },
    {
      icon: Play,
      title: t("aiYouTubeAds.benefit2Title"),
      description: t("aiYouTubeAds.benefit2Description"),
    },
    {
      icon: TrendingUp,
      title: t("aiYouTubeAds.benefit3Title"),
      description: t("aiYouTubeAds.benefit3Description"),
    },
    {
      icon: Target,
      title: t("aiYouTubeAds.benefit4Title"),
      description: t("aiYouTubeAds.benefit4Description"),
    },
    {
      icon: Users,
      title: t("aiYouTubeAds.benefit5Title"),
      description: t("aiYouTubeAds.benefit5Description"),
    },
    {
      icon: Video,
      title: t("aiYouTubeAds.benefit6Title"),
      description: t("aiYouTubeAds.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiYouTubeAds.faq1Question"),
      answer: t("aiYouTubeAds.faq1Answer"),
    },
    {
      question: t("aiYouTubeAds.faq2Question"),
      answer: t("aiYouTubeAds.faq2Answer"),
    },
    {
      question: t("aiYouTubeAds.faq3Question"),
      answer: t("aiYouTubeAds.faq3Answer"),
    },
    {
      question: t("aiYouTubeAds.faq4Question"),
      answer: t("aiYouTubeAds.faq4Answer"),
    },
    {
      question: t("aiYouTubeAds.faq5Question"),
      answer: t("aiYouTubeAds.faq5Answer"),
    },
    {
      question: t("aiYouTubeAds.faq6Question"),
      answer: t("aiYouTubeAds.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiYouTubeAds.testimonial1Name"),
      role: t("aiYouTubeAds.testimonial1Role"),
      company: t("aiYouTubeAds.testimonial1Company"),
      content: t("aiYouTubeAds.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiYouTubeAds.testimonial2Name"),
      role: t("aiYouTubeAds.testimonial2Role"),
      company: t("aiYouTubeAds.testimonial2Company"),
      content: t("aiYouTubeAds.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiYouTubeAds.testimonial3Name"),
      role: t("aiYouTubeAds.testimonial3Role"),
      company: t("aiYouTubeAds.testimonial3Company"),
      content: t("aiYouTubeAds.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiYouTubeAds.meta.title")}
        description={t("aiYouTubeAds.meta.description")}
        keywords={t("aiYouTubeAds.meta.keywords")}
        url="https://miuta.ai/features/ai-youtube-ads/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiYouTubeAds.title")}
        subtitle={t("aiYouTubeAds.subtitle")}
        description={t("aiYouTubeAds.description")}
        ctaText={t("aiYouTubeAds.ctaText")}
        demoText={t("aiYouTubeAds.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiYouTubeAds.benefitsTitle")}
        subtitle={t("aiYouTubeAds.benefitsSubtitle")}
        benefits={benefits}
      />
      
      <FeatureTestimonials testimonials={testimonials} />
      
      <FeatureFAQ faqs={faqs} />
      
      <FeatureCTA
        title={t("aiYouTubeAds.ctaTitle")}
        subtitle={t("aiYouTubeAds.ctaSubtitle")}
        primaryCTA={t("aiYouTubeAds.ctaPrimary")}
        secondaryCTA={t("aiYouTubeAds.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AIYouTubeAds;
