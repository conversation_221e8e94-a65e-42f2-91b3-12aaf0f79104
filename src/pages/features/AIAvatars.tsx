import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { User, Clock, DollarSign, Zap, Shield, Sparkles } from "lucide-react";
import { useTranslation, Trans } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIAvatars = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: DollarSign,
      title: t("aiAvatars.benefit1Title"),
      description: t("aiAvatars.benefit1Description"),
    },
    {
      icon: Clock,
      title: t("aiAvatars.benefit2Title"),
      description: t("aiAvatars.benefit2Description"),
    },
    {
      icon: Zap,
      title: t("aiAvatars.benefit3Title"),
      description: t("aiAvatars.benefit3Description"),
    },
    {
      icon: User,
      title: t("aiAvatars.benefit4Title"),
      description: t("aiAvatars.benefit4Description"),
    },
    {
      icon: Shield,
      title: t("aiAvatars.benefit5Title"),
      description: t("aiAvatars.benefit5Description"),
    },
    {
      icon: Sparkles,
      title: t("aiAvatars.benefit6Title"),
      description: t("aiAvatars.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiAvatars.faq1Question"),
      answer: t("aiAvatars.faq1Answer"),
    },
    {
      question: t("aiAvatars.faq2Question"),
      answer: t("aiAvatars.faq2Answer"),
    },
    {
      question: t("aiAvatars.faq3Question"),
      answer: t("aiAvatars.faq3Answer"),
    },
    {
      question: t("aiAvatars.faq4Question"),
      answer: t("aiAvatars.faq4Answer"),
    },
    {
      question: t("aiAvatars.faq5Question"),
      answer: t("aiAvatars.faq5Answer"),
    },
    {
      question: t("aiAvatars.faq6Question"),
      answer: t("aiAvatars.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiAvatars.testimonial1Name"),
      role: t("aiAvatars.testimonial1Role"),
      company: t("aiAvatars.testimonial1Company"),
      content: t("aiAvatars.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiAvatars.testimonial2Name"),
      role: t("aiAvatars.testimonial2Role"),
      company: t("aiAvatars.testimonial2Company"),
      content: t("aiAvatars.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiAvatars.testimonial3Name"),
      role: t("aiAvatars.testimonial3Role"),
      company: t("aiAvatars.testimonial3Company"),
      content: t("aiAvatars.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiAvatars.meta.title")}
        description={t("aiAvatars.meta.description")}
        keywords={t("aiAvatars.meta.keywords")}
        url="https://miuta.ai/features/ai-avatars/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiAvatars.title")}
        subtitle={t("aiAvatars.subtitle")}
        description={t("aiAvatars.description")}
        ctaText={t("aiAvatars.ctaText")}
        demoText={t("aiAvatars.demoText")}
      />

      <FeatureBenefits
        title={t("aiAvatars.benefitsTitle")}
        subtitle={
          <Trans i18nKey="aiAvatars.benefitsSubtitle">
            Skip the delays and expensive shoots. Create high-performing videos
            as per your timeline with AI Avatars. Perfect for{" "}
            <a
              href="/use-cases/ecommerce"
              className="text-primary hover:text-primary/80 transition-colors underline"
            >
              e-commerce product demonstrations
            </a>{" "}
            and{" "}
            <a
              href="/use-cases/startups"
              className="text-primary hover:text-primary/80 transition-colors underline"
            >
              startup pitch videos
            </a>
            .
          </Trans>
        }
        benefits={benefits}
      />

      <FeatureTestimonials testimonials={testimonials} />

      <FeatureFAQ faqs={faqs} />

      <FeatureCTA
        title={t("aiAvatars.ctaTitle")}
        subtitle={t("aiAvatars.ctaSubtitle")}
        primaryCTA={t("aiAvatars.ctaPrimary")}
        secondaryCTA={t("aiAvatars.ctaSecondary")}
      />

      <Footer />
    </div>
  );
};

export default AIAvatars;
