import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Palette, Zap, Download, Sparkles, Crown, Layers } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AILogoGenerator = () => {
  const { t } = useTranslation("features");

  const faqData = [
    {
      question: t("aiLogoGenerator.faq1Question"),
      answer: t("aiLogoGenerator.faq1Answer")
    },
    {
      question: t("aiLogoGenerator.faq2Question"),
      answer: t("aiLogoGenerator.faq2Answer")
    },
    {
      question: t("aiLogoGenerator.faq3Question"),
      answer: t("aiLogoGenerator.faq3Answer")
    },
    {
      question: t("aiLogoGenerator.faq4Question"),
      answer: t("aiLogoGenerator.faq4Answer")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiLogoGenerator.meta.title")}
        description={t("aiLogoGenerator.meta.description")}
        keywords={t("aiLogoGenerator.meta.keywords")}
        url="https://miuta.ai/features/ai-logo-generator/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiLogoGenerator.title")}
        subtitle={t("aiLogoGenerator.subtitle")}
        description={t("aiLogoGenerator.description")}
        ctaText={t("aiLogoGenerator.ctaText")}
        demoText={t("aiLogoGenerator.demoText")}
      />
      
      <FeatureBenefits
        title={t("aiLogoGenerator.benefitsTitle")}
        subtitle={t("aiLogoGenerator.benefitsSubtitle")}
        benefits={[
          {
            icon: Sparkles,
            title: t("aiLogoGenerator.benefit1Title"),
            description: t("aiLogoGenerator.benefit1Description")
          },
          {
            icon: Palette,
            title: t("aiLogoGenerator.benefit2Title"),
            description: t("aiLogoGenerator.benefit2Description")
          },
          {
            icon: Download,
            title: t("aiLogoGenerator.benefit3Title"),
            description: t("aiLogoGenerator.benefit3Description")
          },
          {
            icon: Zap,
            title: t("aiLogoGenerator.benefit4Title"),
            description: t("aiLogoGenerator.benefit4Description")
          }
        ]}
      />
      
      <FeatureTestimonials
        title={t("aiLogoGenerator.testimonialsTitle")}
        testimonials={[
          {
            content: t("aiLogoGenerator.testimonial1Content"),
            name: t("aiLogoGenerator.testimonial1Name"),
            role: t("aiLogoGenerator.testimonial1Role"),
            company: t("aiLogoGenerator.testimonial1Company"),
            rating: 5
          },
          {
            content: t("aiLogoGenerator.testimonial2Content"),
            name: t("aiLogoGenerator.testimonial2Name"),
            role: t("aiLogoGenerator.testimonial2Role"),
            company: t("aiLogoGenerator.testimonial2Company"),
            rating: 5
          },
          {
            content: t("aiLogoGenerator.testimonial3Content"),
            name: t("aiLogoGenerator.testimonial3Name"),
            role: t("aiLogoGenerator.testimonial3Role"),
            company: t("aiLogoGenerator.testimonial3Company"),
            rating: 5
          }
        ]}
      />
      
      <FeatureFAQ
        title={t("aiLogoGenerator.faqTitle")}
        faqs={faqData}
      />
      
      <FeatureCTA
        title={t("aiLogoGenerator.ctaTitle")}
        subtitle={t("aiLogoGenerator.ctaSubtitle")}
        primaryCTA={t("aiLogoGenerator.ctaPrimary")}
        secondaryCTA={t("aiLogoGenerator.ctaSecondary")}
      />
      
      <Footer />
    </div>
  );
};

export default AILogoGenerator;
