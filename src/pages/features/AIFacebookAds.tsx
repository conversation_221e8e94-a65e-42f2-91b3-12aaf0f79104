import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { FeatureHero } from "@/components/FeatureHero";
import { FeatureBenefits } from "@/components/FeatureBenefits";
import { FeatureFAQ } from "@/components/FeatureFAQ";
import { FeatureTestimonials } from "@/components/FeatureTestimonials";
import { FeatureCTA } from "@/components/FeatureCTA";
import { HeadContent } from "@/components/HeadContent";
import { Facebook, Target, TrendingUp, Zap, DollarSign, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  baseOrganization,
  websiteSchema,
  softwareApplicationSchema,
  videoCreationService,
} from "@/lib/schema";

const AIFacebookAds = () => {
  const { t } = useTranslation("features");

  const benefits = [
    {
      icon: Target,
      title: t("aiFacebookAds.benefit1Title"),
      description: t("aiFacebookAds.benefit1Description"),
    },
    {
      icon: TrendingUp,
      title: t("aiFacebookAds.benefit2Title"),
      description: t("aiFacebookAds.benefit2Description"),
    },
    {
      icon: Zap,
      title: t("aiFacebookAds.benefit3Title"),
      description: t("aiFacebookAds.benefit3Description"),
    },
    {
      icon: DollarSign,
      title: t("aiFacebookAds.benefit4Title"),
      description: t("aiFacebookAds.benefit4Description"),
    },
    {
      icon: Users,
      title: t("aiFacebookAds.benefit5Title"),
      description: t("aiFacebookAds.benefit5Description"),
    },
    {
      icon: Facebook,
      title: t("aiFacebookAds.benefit6Title"),
      description: t("aiFacebookAds.benefit6Description"),
    },
  ];

  const faqs = [
    {
      question: t("aiFacebookAds.faq1Question"),
      answer: t("aiFacebookAds.faq1Answer"),
    },
    {
      question: t("aiFacebookAds.faq2Question"),
      answer: t("aiFacebookAds.faq2Answer"),
    },
    {
      question: t("aiFacebookAds.faq3Question"),
      answer: t("aiFacebookAds.faq3Answer"),
    },
    {
      question: t("aiFacebookAds.faq4Question"),
      answer: t("aiFacebookAds.faq4Answer"),
    },
    {
      question: t("aiFacebookAds.faq5Question"),
      answer: t("aiFacebookAds.faq5Answer"),
    },
    {
      question: t("aiFacebookAds.faq6Question"),
      answer: t("aiFacebookAds.faq6Answer"),
    },
  ];

  const testimonials = [
    {
      name: t("aiFacebookAds.testimonial1Name"),
      role: t("aiFacebookAds.testimonial1Role"),
      company: t("aiFacebookAds.testimonial1Company"),
      content: t("aiFacebookAds.testimonial1Content"),
      rating: 5,
    },
    {
      name: t("aiFacebookAds.testimonial2Name"),
      role: t("aiFacebookAds.testimonial2Role"),
      company: t("aiFacebookAds.testimonial2Company"),
      content: t("aiFacebookAds.testimonial2Content"),
      rating: 5,
    },
    {
      name: t("aiFacebookAds.testimonial3Name"),
      role: t("aiFacebookAds.testimonial3Role"),
      company: t("aiFacebookAds.testimonial3Company"),
      content: t("aiFacebookAds.testimonial3Content"),
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <HeadContent
        title={t("aiFacebookAds.meta.title")}
        description={t("aiFacebookAds.meta.description")}
        keywords={t("aiFacebookAds.meta.keywords")}
        url="https://miuta.ai/features/ai-facebook-ads/"
        image={`https://lovable.dev/opengraph-image-p98pqg.png?v=${import.meta.env.VITE_APP_HASH}`}
        schemas={[
          baseOrganization,
          websiteSchema,
          softwareApplicationSchema,
          videoCreationService,
        ]}
          />
      
      <Header />
      <FeatureHero
        title={t("aiFacebookAds.title")}
        subtitle={t("aiFacebookAds.subtitle")}
        description={t("aiFacebookAds.description")}
        ctaText={t("aiFacebookAds.ctaText")}
        demoText={t("aiFacebookAds.demoText")}
      />

      <FeatureBenefits
        title={t("aiFacebookAds.benefitsTitle")}
        subtitle={t("aiFacebookAds.benefitsSubtitle")}
        benefits={benefits}
      />

      <FeatureTestimonials testimonials={testimonials} />

      <FeatureFAQ faqs={faqs} />

      <FeatureCTA
        title={t("aiFacebookAds.ctaTitle")}
        subtitle={t("aiFacebookAds.ctaSubtitle")}
        primaryCTA={t("aiFacebookAds.ctaPrimary")}
        secondaryCTA={t("aiFacebookAds.ctaSecondary")}
      />

      <Footer />
    </div>
  );
};

export default AIFacebookAds;
