export interface OrganizationSchema {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  url: string;
  logo: string;
  sameAs: string[];
  contactPoint: {
    "@type": string;
    contactType: string;
    url: string;
  };
  address?: {
    "@type": string;
    addressCountry: string;
  };
}

export interface WebsiteSchema {
  "@context": string;
  "@type": string;
  name: string;
  url: string;
  description: string;
  publisher: {
    "@type": string;
    name: string;
  };
  potentialAction?: {
    "@type": string;
    target: {
      "@type": string;
      urlTemplate: string;
    };
    "query-input": string;
  };
}

export interface SoftwareApplicationSchema {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers: {
    "@type": string;
    price: string;
    priceCurrency: string;
    priceValidUntil: string;
    availability: string;
  }[];
  aggregateRating?: {
    "@type": string;
    ratingValue: string;
    reviewCount: string;
  };
  creator: {
    "@type": string;
    name: string;
  };
}

export interface ServiceSchema {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  provider: {
    "@type": string;
    name: string;
    url: string;
  };
  areaServed: string;
  hasOfferCatalog: {
    "@type": string;
    name: string;
    itemListElement: {
      "@type": string;
      itemOffered: {
        "@type": string;
        name: string;
        description: string;
      };
    }[];
  };
}

export interface FAQSchema {
  "@context": string;
  "@type": string;
  mainEntity: {
    "@type": string;
    name: string;
    acceptedAnswer: {
      "@type": string;
      text: string;
    };
  }[];
}

export interface ProductSchema {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  brand: {
    "@type": string;
    name: string;
  };
  offers: {
    "@type": string;
    price: string;
    priceCurrency: string;
    availability: string;
    priceValidUntil: string;
  }[];
  aggregateRating?: {
    "@type": string;
    ratingValue: string;
    reviewCount: string;
  };
}

export interface BreadcrumbSchema {
  "@context": string;
  "@type": string;
  itemListElement: {
    "@type": string;
    position: number;
    name: string;
    item: string;
  }[];
}

// Base organization data
export const baseOrganization: OrganizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Miuta",
  description: "AI-powered video creation platform that generates professional marketing videos with realistic AI actors and voiceover.",
  url: "https://miuta.ai",
  logo: "https://miuta.ai/logo.png",
  sameAs: [
    "https://twitter.com/miuta_ai",
    "https://linkedin.com/company/miuta",
    "https://facebook.com/miuta"
  ],
  contactPoint: {
    "@type": "ContactPoint",
    contactType: "customer service",
    url: "https://miuta.ai/contact"
  },
  address: {
    "@type": "PostalAddress",
    addressCountry: "US"
  }
};

// Website schema
export const websiteSchema: WebsiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  name: "Miuta - AI Video Creation Platform",
  url: "https://miuta.ai",
  description: "Create viral marketing videos with AI actors, voiceover & UGC content. Generate high-quality AI marketing videos effortlessly.",
  publisher: {
    "@type": "Organization",
    name: "Miuta"
  },
  potentialAction: {
    "@type": "SearchAction",
    target: {
      "@type": "EntryPoint",
      urlTemplate: "https://miuta.ai/search?q={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  }
};

// Software application schema
export const softwareApplicationSchema: SoftwareApplicationSchema = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "Miuta AI Video Creator",
  description: "Professional AI video creation platform with realistic actors, voiceover, and marketing optimization features.",
  url: "https://miuta.ai",
  applicationCategory: "Video Creation Software",
  operatingSystem: "Web Browser",
  offers: [
    {
      "@type": "Offer",
      price: "29",
      priceCurrency: "USD",
      priceValidUntil: "2025-12-31",
      availability: "https://schema.org/InStock"
    },
    {
      "@type": "Offer", 
      price: "79",
      priceCurrency: "USD",
      priceValidUntil: "2025-12-31",
      availability: "https://schema.org/InStock"
    },
    {
      "@type": "Offer",
      price: "199", 
      priceCurrency: "USD",
      priceValidUntil: "2025-12-31",
      availability: "https://schema.org/InStock"
    }
  ],
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.8",
    reviewCount: "1250"
  },
  creator: {
    "@type": "Organization",
    name: "Miuta"
  }
};

// Video creation service schema
export const videoCreationService: ServiceSchema = {
  "@context": "https://schema.org",
  "@type": "Service",
  name: "AI Video Creation Services",
  description: "Professional AI-powered video creation with realistic actors, voiceover, and marketing optimization.",
  provider: {
    "@type": "Organization", 
    name: "Miuta",
    url: "https://miuta.ai"
  },
  areaServed: "Worldwide",
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "AI Video Creation Services",
    itemListElement: [
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "AI Avatar Videos",
          description: "Create videos with realistic AI actors and avatars"
        }
      },
      {
        "@type": "Offer", 
        itemOffered: {
          "@type": "Service",
          name: "AI Script Generation",
          description: "Generate high-converting video scripts with AI"
        }
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service", 
          name: "Social Media Video Ads",
          description: "Create optimized video ads for all social platforms"
        }
      }
    ]
  }
};

// FAQ schema generator
export const generateFAQSchema = (faqs: { question: string; answer: string }[]): FAQSchema => ({
  "@context": "https://schema.org",
  "@type": "FAQPage", 
  mainEntity: faqs.map(faq => ({
    "@type": "Question",
    name: faq.question,
    acceptedAnswer: {
      "@type": "Answer",
      text: faq.answer
    }
  }))
});

// Breadcrumb generator
export const generateBreadcrumbSchema = (breadcrumbs: { name: string; url: string }[]): BreadcrumbSchema => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  itemListElement: breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    position: index + 1,
    name: crumb.name,
    item: crumb.url
  }))
});

// Product schema generator for features
export const generateFeatureProductSchema = (
  name: string,
  description: string,
  category: string
): ProductSchema => ({
  "@context": "https://schema.org",
  "@type": "Product",
  name: name,
  description: description,
  brand: {
    "@type": "Brand",
    name: "Miuta"
  },
  offers: [
    {
      "@type": "Offer",
      price: "29",
      priceCurrency: "USD", 
      availability: "https://schema.org/InStock",
      priceValidUntil: "2025-12-31"
    }
  ],
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.8",
    reviewCount: "1250"
  }
});

// Use case service schema generator
export const generateUseCaseServiceSchema = (
  name: string,
  description: string,
  benefits: string[]
): ServiceSchema => ({
  "@context": "https://schema.org",
  "@type": "Service",
  name: name,
  description: description,
  provider: {
    "@type": "Organization",
    name: "Miuta",
    url: "https://miuta.ai"
  },
  areaServed: "Worldwide",
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: `${name} Solutions`,
    itemListElement: benefits.map(benefit => ({
      "@type": "Offer",
      itemOffered: {
        "@type": "Service",
        name: benefit,
        description: `Professional ${benefit.toLowerCase()} services with AI video creation`
      }
    }))
  }
});
