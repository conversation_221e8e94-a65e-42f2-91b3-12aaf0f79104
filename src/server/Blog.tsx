import React from 'react';

interface BlogPost {
  id: string;
  link: string;
  title: string;
  description: string;
  date: string;
  imageUrl: string;
}

interface BlogPageProps {
  posts: BlogPost[];
}

const BlogPage: React.FC<BlogPageProps> = ({
  posts
}) => {
  return (
    <div className="pt-32 bg-gray-100 text-black">
      {/* Header Section */}
      <div className="bg-gray-100 py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Blog</h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Discover tips, tutorials, and insights to get the most out of XXX and transform your note-taking experience.
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 pb-16">
        {/* Featured Article */}
        {posts[0] && (
          <div className="mb-16">
            <div className="relative bg-white rounded-2xl overflow-hidden group hover:transform hover:scale-[1.02] transition-all duration-300">
              <a href={`${posts[0].link}`}>
                <div className="flex flex-col lg:flex-row">
                  {/* Image Section */}
                  <div className="lg:w-1/2 relative">
                    <img
                      src={posts[0].imageUrl}
                      alt={posts[0].title}
                      className="w-full h-64 lg:h-80 object-cover"
                    />
                  </div>

                  {/* Content Section */}
                  <div className="lg:w-1/2 p-8 lg:p-12 flex flex-col justify-center">
                    <h2 className="text-2xl lg:text-3xl font-bold mb-4 leading-tight">
                      {posts[0].title}
                    </h2>
                    <p className="text-gray-400 mb-6 leading-relaxed text-base">
                      {posts[0].description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-sm">
                        📅 {posts[0].date}
                      </span>
                      <button className="text-base text-yellow-500 hover:text-yellow-400 font-medium flex items-center gap-2 transition-colors">
                        Read article
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
        )}

        {/* Latest Posts Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-8">Latest Posts</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {posts.slice(1).map((post) => (
              <div
                key={post.id}
                className="bg-white rounded-xl overflow-hidden group hover:transform hover:scale-[1.02] transition-all duration-300 cursor-pointer"
              >
                <a href={`${post.link}`}>
                  {/* Card Image */}
                  <div className="relative">
                    <img
                      src={post.imageUrl}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                    />
                  </div>

                  {/* Card Content */}
                  <div className="p-6">
                    <h3 className="text-lg font-semibold mb-3 leading-tight group-hover:text-yellow-500 transition-colors">
                      {post.title}
                    </h3>
                    <p className="text-gray-400 text-sm mb-4 leading-relaxed">
                      {post.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-xs">
                        📅 {post.date}
                      </span>
                      <button className="text-yellow-500 hover:text-yellow-400 text-sm font-medium flex items-center gap-1 transition-colors">
                        Read
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPage;
