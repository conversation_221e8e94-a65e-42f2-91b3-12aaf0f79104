import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { randomBytes } from 'crypto';

// 生成 hash（可自定义算法）
const hash = Date.now()

// 写入 .env.production 的函数
function writeEnv(projectPath, key, value) {
  const envPath = join(projectPath, '.env.production');
  let envContent = '';
  if (existsSync(envPath)) {
    envContent = readFileSync(envPath, 'utf-8');
  }
  const regex = new RegExp(`^${key}=.*$`, 'm');
  if (regex.test(envContent)) {
    envContent = envContent.replace(regex, `${key}=${value}`);
  } else {
    if (envContent.length > 0 && !envContent.endsWith('\n')) {
      envContent += '\n';
    }
    envContent += `${key}=${value}\n`;
  }
  writeFileSync(envPath, envContent, 'utf-8');
  console.log(`已写入 ${key}=${value} 到 ${envPath}`);
}

// 写入到两个项目
writeEnv('./', 'VITE_APP_HASH', hash);
