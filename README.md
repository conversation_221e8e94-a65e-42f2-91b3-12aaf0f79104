# 基于vite-react-ssg 实现多语言ssg

## 安装vite-react-ssg
```
npm install vite-react-ssg
```
## 安装i18n
```
npm install react-i18next i18next i18next-http-backend
```
## 创建多语言环境变量
```
touch .env.development
touch .env.production
```
```
// 内容
VITE_APP_I18N=en,zh,es
VITE_APP_I18N_DEFAULT=en
```
## 在public目录下创建locales目录存放多语言json文件
```
mkdir public/locales
```
## 创建语言项目录
```
mkdir public/locales/en
```
## 创建多语言json文件
```
touch public/locales/en/translation.json
```
## 在src目录下创建locales目录存放i18n配置文件
```
mkdir src/locales
```
## 创建i18n配置文件
```
touch src/locales/index.ts
```
## 实现i18n配置文件
```
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
i18n
    .use(Backend)
    .use(initReactI18next)
    .init({
        fallbackLng: 'en',
        ns: ['translation'],      // 只用一个命名空间
        defaultNS: 'translation', // 默认命名空间
        backend: {
            loadPath: '/locales/{{lng}}/{{ns}}.json',
        },
        react: {
            useSuspense: false,
        },
    });

export default i18n;
// 实现多语言组件和多语言钩子处理语言项切换
src/components/LanguageSync.tsx
// 实现细节
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

export default function LanguageSync({ children }: { children: JSX.Element }) {
  const { i18n } = useTranslation();
  const location = useLocation();

  // 由于useEffect不会在服务端执行 所以需要在SSG处理的时候在这里单独处理一次自动切换
  if (typeof window === 'undefined') {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }
  useEffect(() => {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }, [location, i18n]);

  return children;
}
```
## 实现LanguageSync高阶组件处理多语言页面跳转
```
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

export default function LanguageSync({ children }: { children: JSX.Element }) {
  const { i18n } = useTranslation();
  const location = useLocation();

  // 由于useEffect不会在服务端执行 所以需要在SSG处理的时候在这里单独处理一次自动切换
  if (typeof window === 'undefined') {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }
  useEffect(() => {
    const lang = import.meta.env.VITE_APP_I18N.split(',').find((lang: string) => location.pathname.endsWith(`/${lang}/`));
    i18n.changeLanguage(lang || 'en');
  }, [location, i18n]);

  return children;
}
```
## 实现多语言页面跳转钩子
```
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export function useI18nNavigate() {
  const navigate = useNavigate();
  const { i18n } = useTranslation();

  return (to: string, options?: { replace?: boolean }) => {
    const url = to.startsWith("http")
        ? new URL(to)
        : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
    navigate(url.toString().replace("http://localhost", ""), options);
  };
}
```
## 实现多语言页面跳转高阶组件 I18nA
```
import { useEffect, useState, type AnchorHTMLAttributes } from "react";
import { useTranslation } from "react-i18next";

export default (props: AnchorHTMLAttributes<HTMLAnchorElement>) => {
    const { i18n } = useTranslation();
    const [path, setPath] = useState(props.href);

    useEffect(() => {
        const to = props.href as string;
        const url = to.startsWith("http")
            ? new URL(to)
            : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
        setPath(url.toString().replace("http://localhost", ""));
    }, [i18n.language, props.href]);

    return <a {...props} href={path}>{props.children}</a>
}
```
## 实现多语言页面跳转高阶组件 I18nLink
```
import { useEffect, useState, type RefAttributes } from "react";
import { useTranslation } from "react-i18next";
import { Link, type LinkProps } from "react-router-dom";

export default (props: LinkProps & RefAttributes<HTMLAnchorElement>) => {
    const { i18n } = useTranslation();
    const [path, setPath] = useState(props.to);

    useEffect(() => {
        const to = props.to as string;
        const url = to.startsWith("http")
            ? new URL(to)
            : new URL(to, "http://localhost");
        url.pathname = url.pathname +  `${i18n.language === "en" ? "" : `${i18n.language}/`}`;
        setPath(url.toString().replace("http://localhost", ""));
    }, [i18n.language, props.to]);

    return <Link {...props} to={path}>{props.children}</Link>
}
```
## 主入口 main.tsx
```
// https://vite-react-ssg.netlify.app/docs/getting-started
import { Head, ViteReactSSG } from "vite-react-ssg"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as Sonner } from "@/components/ui/sonner"
import { TooltipProvider } from "@/components/ui/tooltip"
import { default as LanguageSync } from "@/components/LanguageSync";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import Index from './pages/Index'
import NotFound from "./pages/NotFound"
import './locales'
import './index.css'

const queryClient = new QueryClient();

const Layout = ({ children }: { children?: React.ReactNode }) => (
    <>
        <Head>
            {/** 字符集 */}
            <meta charSet="UTF-8" />
            {/** 移动端适配 */}
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            {/** 基本SEO标签 */}
            <title>页面标题</title>
            <meta name="description" content="页面描述，建议 50-160 字，精准概括页面内容" />
            <meta name="keywords" content="关键词1,关键词2,关键词3" />
            <meta name="author" content="作者或公司名" />
            <meta name="robots" content="index, follow" />
            <meta name="copyright" content="你的公司或个人名" />
            <meta property="og:title" content="页面标题" />
            {/** 社交媒体分享 微信/QQ/微博/FB */}
            <meta property="og:description" content="页面描述" />
            <meta property="og:type" content="website" />
            <meta property="og:url" content="https://yourdomain.com/page" />
            <meta property="og:image" content="https://yourdomain.com/og-image.png" />
            <meta property="og:site_name" content="站点名" />
            <meta name="twitter:card" content="summary_large_image" />
            {/** 社交媒体分享 Twitter */}
            <meta name="twitter:title" content="页面标题" />
            <meta name="twitter:description" content="页面描述" />
            <meta name="twitter:image" content="https://yourdomain.com/twitter-image.png" />
            <meta name="twitter:site" content="@你的推特账号" />
            {/** 图标 */}
            <link rel="icon" href="/favicon.ico" />
            <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
            <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
            <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
            {/** 站点地图 */}
            <link rel="sitemap" type="application/xml" title="Sitemap" href="/sitemap.xml" />
            {/** 规范链接 防止重复收录 相同内容页面href相同 告知搜索引擎是一个页面 */}
            <link rel="canonical" href="https://yourdomain.com/page" />
            {/** 多语言链接 */}
            <link rel="alternate" href="https://yourdomain.com/" hrefLang="x-default" />
            <link rel="alternate" href="https://yourdomain.com/" hrefLang="zh-CN" />
            <link rel="alternate" href="https://yourdomain.com/en/" hrefLang="en" />
            <link rel="alternate" href="https://yourdomain.com/es/" hrefLang="es" />
            {/** PWA 网站清单 */}
            <link rel="manifest" href="/site.webmanifest" />
            {/**  结构化数据（JSON-LD，提升富媒体展示） */}
            <script type="application/ld+json">
            {`{
                "@context": "https://schema.org",
                "@type": "WebSite",
                "name": "你的站点名",
                "url": "https://yourdomain.com/"
            
            }`}
            </script>
        </Head>
        <LanguageSync>
            <QueryClientProvider client={queryClient}>
                <TooltipProvider>
                    <Toaster />
                    <Sonner />
                    {children}
                </TooltipProvider>
            </QueryClientProvider>
        </LanguageSync>
    </>
);

export const routes = [
  {
    path: "/:lang?",
    element: <Layout><Index /></Layout>,
    title: "Index",
    getStaticPaths: () => import.meta.env.VITE_APP_I18N.split(',').map((lang: string) =>  lang !== import.meta.env.VITE_APP_I18N_DEFAULT ? `/${lang}/` : '/'),
  },
  {
    path: "*",
    element: <Layout><NotFound /></Layout>,
    title: "Not Found",
  }
];

export const createRoot = ViteReactSSG({
  routes,
});
```
## 更改打包配置
```
import { defineConfig, loadEnv } from "vite"
import react from "@vitejs/plugin-react-swc"
import generateSitemap from "vite-ssg-sitemap"
import { parseStringPromise, Builder } from "xml2js"
import path from "path"
import fs from "fs/promises"
import { componentTagger } from "lovable-tagger"
import i18n from './src/locales'

const paths: string[] = []
const siteMapExclude: string[] = []

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  ssgOptions: {
    includedRoutes(p: string[]) {
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      p.forEach(path => {
        languages.forEach(lang => {
          if (path.endsWith(`/${lang}/`)) {
            siteMapExclude.push(path)
          }
        })
      });
      // 记录路径
      paths.push(...p)
      return p
    },
    async onBeforePageRender(route: string) {
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      const defaultLanguage = loadEnv(mode, process.cwd(), '').VITE_APP_I18N_DEFAULT
      const dirs = route.split(path.sep)
      const lang = languages.includes(dirs[dirs.length - 2]) ? dirs[dirs.length - 2] : defaultLanguage
      const json = await fs.readFile(`public/locales/${lang}/translation.json`, 'utf-8')
      const translation = JSON.parse(json)
      i18n.changeLanguage(lang)
      i18n.addResourceBundle(lang, 'translation', translation);
    },
    async onFinished() {
      console.log(`开始处理html语言...`)
      const languages = loadEnv(mode, process.cwd(), '').VITE_APP_I18N.split(',')
      const defaultLanguage = loadEnv(mode, process.cwd(), '').VITE_APP_I18N_DEFAULT
      const promise = paths.map(async p => {
        // 特殊路径不处理
        if (p === '*') {
          return
        }
        if (p.endsWith(':lang?')) {
          return
        }
        const dirs = p.split(path.sep)
        const lang = languages.includes(dirs[dirs.length - 2]) ? dirs[dirs.length - 2] : defaultLanguage
        // p 参考值："/" "/blog/"
        const html = await fs.readFile(`dist${p}index.html`, 'utf-8')
        const i18nHTML = html.replace(/<html.*?>/, `<html lang="${lang}">`)
        await fs.writeFile(`dist${p}index.html`, i18nHTML, 'utf-8')
        console.log(`html语言 已处理: ${p}，lang=${lang}`)
      })
      await Promise.all(promise)
      console.log(`sitemap.xml生成`)
      generateSitemap({
        outDir: 'dist',
        hostname: 'https://babymom.ai',
        // 若要做多语言路由 sitemap需要特殊处理 这里要过滤掉非默认语言的路由处理 然后让sitemap自己去生成多语言路由的sitemap.xml内容 否则生成内容不符合要求
        i18n: {
          strategy: 'suffix',
          defaultLanguage: 'en',
          languages: languages.filter(lang => lang !== 'en'),
        }
      })
      // 排除sitemap.xml中不需要的多语言路径
      const MAX_WAIT_SECONDS = 30 // 最长等待时间（秒）
      const CHECK_INTERVAL_MS = 500 // 触发间隔
      const sitemapPath = path.resolve(__dirname, 'dist', 'sitemap.xml')
      const maxAttempts = (MAX_WAIT_SECONDS * 1000) / CHECK_INTERVAL_MS

      let intervalId: NodeJS.Timeout | null = null
      let attempts = 0

      console.log(`开始轮询检查 sitemap.xml 文件是否存在... (最长等待 ${MAX_WAIT_SECONDS} 秒)`)

      intervalId = setInterval(async () => {
        attempts++
        try {
          // 尝试访问文件，如果不存在会抛出错误
          await fs.access(sitemapPath)
          // 文件存在！
          if (intervalId) {
            clearInterval(intervalId)
          }
          console.log(`文件 sitemap.xml 已找到！开始执行处理代码...`)
          try {
            // 1. 读取和解析 XML
            const xmlContent = await fs.readFile(sitemapPath, 'utf-8')
            const parsedXml = await parseStringPromise(xmlContent)

            if (!parsedXml.urlset || !parsedXml.urlset.url) {
              console.log('Sitemap 文件格式不正确，跳过处理。')
              return
            }

            // 排除逻辑
            const shouldExcludeUrl = (url: string): boolean => {
              // 这里不需要判断尾斜杠
              return siteMapExclude.some(exclude => url.endsWith(exclude.slice(0, -1)))
            };
      
            // 过滤并重建
            const urlsToKeep = parsedXml.urlset.url.filter((u: any) => !shouldExcludeUrl(u.loc[0]))
            // 给所有路径统一加上尾斜杠/
            urlsToKeep.forEach((u: any) => {
              if (!u.loc[0].endsWith('/')) {
                u.loc[0] = u.loc[0] + '/'
              }
              u['xhtml:link'].forEach((link: any) => {
                if (!link.$.href.endsWith('/')) {
                  link.$.href = link.$.href + '/'
                }
              })
            })
            const removedCount = parsedXml.urlset.url.length - urlsToKeep.length
      
            parsedXml.urlset.url = urlsToKeep
            const builder = new Builder()
            const newXmlContent = builder.buildObject(parsedXml)
            await fs.writeFile(sitemapPath, newXmlContent)
            console.log(`成功从 sitemap.xml 中移除了 ${removedCount} 个 URL。`)
          } catch (e: any) {
            console.error(`处理 sitemap.xml 时出错: ${e.message}`)
          }
        } catch (error) {
          // 文件不存在，继续轮询
          if (attempts > maxAttempts) {
            if (intervalId) {
              clearInterval(intervalId)
            }
            console.error(`等待超时，未找到 sitemap.xml 文件。`)
          }
          // 如果是其他错误，也停止并报告
          else if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
             if (intervalId) {
              clearInterval(intervalId)
             }
             console.error(`检查文件时发生意外错误: ${(error as Error).message}`)
          }
        }
      }, CHECK_INTERVAL_MS);
    }
  }
}));
```
## 安装相关开放环境插件
```
npm install vite-ssg-sitemap -D
npm install xml2js -D 
```

# 若需支持SSR(有动态内容需求)
不采用常规SSR方案 采用服务端页面组件 -> HOC(包装客户端功能) -> 客户端页面组件的形式来实现服务端渲染 避免一套代码两端运行的维护成本 避免走水合处理防止页面假死（客户端执行出来的内直接替换服务端执行出来的内容）

## 用koa搭建node服务 
```
npm install koa -D
npm install koa-router -D
npm install koa-static -D
npm install koa-bodyparser -D
// 模板字符串出来 用于插入标签
npm install cheerio -D
// 有请求需要安装
npm install axios -D
```
```
import Koa from 'koa';
import serve from 'koa-static';
import Router from 'koa-router';
import send from 'koa-send';
import bodyParser from 'koa-bodyparser';
import fs from 'fs';
import path from 'path';

// 获取文件 MIME 类型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase()
  const mimeTypes = {
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.html': 'text/html',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
  }
  return mimeTypes[ext] || 'application/octet-stream'
}

const router = new Router();

const app = new Koa();

// /xxx /xxx/index.html 归因到 /xxx/
app.use(async (ctx, next) => {
  // 匹配任意路径下的 index.html
  const match = ctx.path.match(/^(.*)\/index\.html$/);
  if (match) {
    // 如果是根目录 /index.html，重定向到 /
    // 其他如 /blog/index.html 重定向到 /blog/
    const redirectPath = match[1] === '' ? '/' : match[1] + '/';
    ctx.status = 301; // 永久重定向
    ctx.redirect(redirectPath);
    return;
  }
  await next();
});

// 添加尾斜杠重定向逻辑
app.use(async (ctx, next) => {
  // 检查路径是否不以斜杠结尾且不包含文件扩展名
  if (!ctx.path.endsWith('/') && !ctx.path.includes('.') && ctx.path !== '/') {
    // 永久重定向到带尾斜杠的路径
    ctx.status = 301;
    ctx.redirect(ctx.path + '/');
    return;
  }
  await next();
});

// 处理 token
app.use(async (ctx, next) => {
  if (ctx.cookies.get('token')) {
    const langMatch = ctx.path.match(/^\/(en|es|zh)\/?$/);

    if (langMatch) {
      // 多语言根路径，重定向到对应语言的编辑器
      const language = langMatch[1];
      ctx.status = 302; // 临时重定向
      ctx.redirect(`/editor/${language}`);
      return;
    }

    // 检查是否为默认根路径 /
    if (ctx.path === '/') {
      ctx.status = 302; // 临时重定向
      ctx.redirect('/editor/');
      return;
    }
  }
  await next();
});

app.use(bodyParser());

app.use(router.routes());

// 1. 优先返回 gzip 文件中间件
app.use(async (ctx, next) => {
  if (ctx.method === 'GET') {
    const acceptEncoding = ctx.headers['accept-encoding'] || ''
    
    // 检查是否支持 gzip
    if (acceptEncoding.includes('gzip')) {
      const gzPath = ctx.path + '.gz'
      const absPath = path.join('./static', gzPath)
      
      // 如果存在对应的 .gz 文件
      if (fs.existsSync(absPath)) {
        try {
          // 设置正确的响应头
          ctx.set('Content-Type', getMimeType(ctx.path))
          ctx.set('Content-Encoding', 'gzip')
          ctx.set('Vary', 'Accept-Encoding')
          
          // 设置缓存头
          ctx.set('Cache-Control', 'public, max-age=604800')
          
          // 发送 gzip 文件
          await send(ctx, gzPath, { 
            root: './static',
            maxAge: 604800, // 7天缓存
            immutable: false,
          })
          return
        } catch (error) {
          console.error('发送 gzip 文件失败:', error)
          // 如果发送失败，继续下一个中间件
        }
      }
    }
  }
  
  await next()
});

app.use(serve('./static', {
  // maxage: 86400 * 1000,
  setHeaders: (res, path, stats) => {
    if (path.endsWith('.js') || path.endsWith('.css') || path.endsWith('.svg') || path.endsWith('.webp')) {
      // 缓存7天
      res.setHeader('Cache-Control', 'public, max-age=604800')
    }
  },
}));

const start = async () => {
  app.listen(3000);
}

start();

console.log('Server is running on http://localhost:3000');
```
## 更改package.json命令
"dev": "vite-react-ssg dev",
"build": "vite-react-ssg build",
"start": "NODE_ENV=production node app.js"

## 默认gzip压缩
```
npm install vite-plugin-compression -D
```
```
plugins: [
    react(),
    // gzip 压缩配置
    viteCompression({
      algorithm: 'gzip',           // 压缩算法
      ext: '.gz',                  // 文件扩展名
      threshold: 10240,            // 只压缩大于 10kb 的文件
      deleteOriginFile: false,     // 保留原始文件
      filter: /\.(js|css|html|svg)$/, // 只压缩这些类型的文件
      compressionOptions: {
        level: 9,                  // 压缩级别 (1-9, 9为最高压缩比)
      },
      verbose: true,               // 显示压缩信息
    }),
],
```
```
import Koa from 'koa';
import serve from 'koa-static';
import Router from 'koa-router';
import send from 'koa-send';
import bodyParser from 'koa-bodyparser';
import * as cheerio from 'cheerio';
import fs from 'fs/promises';
import path from 'path';
import React from 'react';
import { renderToString } from 'react-dom/server';
import Blog from './lib/Blog.es.js';

// 获取文件 MIME 类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase()
    const mimeTypes = {
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.svg': 'image/svg+xml',
        // 待定
        // '.json': 'application/json',
        // '.ico': 'image/x-icon',
        // '.woff': 'font/woff',
        // '.woff2': 'font/woff2',
        // '.ttf': 'font/ttf',
        // '.eot': 'application/vnd.ms-fontobject'
    }
    return mimeTypes[ext] || 'application/octet-stream'
}

const router = new Router();

const loadBlog = async (ctx) => {
    try {
        const { lang } = ctx.params;
        let template = await fs.readFile(`./static/blog${lang ? `/${lang}/` : '/'}index.html`, 'utf-8')
        const html = renderToString(React.createElement(Blog, {
            posts: [],
        }))
        // 加载 HTML
        const $ = cheerio.load(template)
        // 替换 id="app" 的标签内容
        $('#root').html(html)
        const newHtml = $.html()
        ctx.body = newHtml
    } catch (error) {
        console.log(error);
        ctx.status = 500;
        ctx.body = {
            code: 500,
            data: {},
            message: 'Internal server error',
        };
    }
}
router.get('/blog/:lang/', loadBlog);
router.get('/blog/', loadBlog);

const app = new Koa();

// /xxx /xxx/index.html 归因到 /xxx/
app.use(async (ctx, next) => {
    // 匹配任意路径下的 index.html
    const match = ctx.path.match(/^(.*)\/index\.html$/);
    if (match) {
        // 如果是根目录 /index.html，重定向到 /
        // 其他如 /blog/index.html 重定向到 /blog/
        const redirectPath = match[1] === '' ? '/' : match[1] + '/';
        ctx.status = 301; // 永久重定向
        ctx.redirect(redirectPath);
        return;
    }
    await next();
});

// 添加尾斜杠重定向逻辑
app.use(async (ctx, next) => {
    // 检查路径是否不以斜杠结尾且不包含文件扩展名
    if (!ctx.path.endsWith('/') && !ctx.path.includes('.') && ctx.path !== '/') {
        // 永久重定向到带尾斜杠的路径
        ctx.status = 301;
        ctx.redirect(ctx.path + '/');
        return;
    }
    await next();
});

app.use(bodyParser());

app.use(router.routes());

// 1. 优先返回 gzip 文件中间件
app.use(async (ctx, next) => {
    if (ctx.method === 'GET') {
        const acceptEncoding = ctx.headers['accept-encoding'] || ''

        // 检查是否支持 gzip
        if (acceptEncoding.includes('gzip')) {
            const gzPath = ctx.path + '.gz'
            const absPath = path.join('./static', gzPath)

            // 如果存在对应的 .gz 文件
            try {
                if (await fs.stat(absPath)) {
                    // 设置正确的响应头
                    ctx.set('Content-Type', getMimeType(ctx.path))
                    ctx.set('Content-Encoding', 'gzip')
                    ctx.set('Vary', 'Accept-Encoding')

                    // 缓存一个月且缓存期间不验证（需保证所有文件都具备hash值）
                    ctx.set('Cache-Control', 'public, max-age=2592000, immutable')
                    // 发送 gzip 文件
                    await send(ctx, gzPath, {
                        root: './static',
                        maxAge: 2592000, // 一个月缓存
                        immutable: true,
                    })
                    return
                }
            }
            catch {
                // 如果发送失败，继续下一个中间件
            }
        }
    }
    await next()
});

app.use(serve('./static', {
    // maxage: 86400 * 1000,
    setHeaders: (res, path, stats) => {
        if (path.endsWith('.gz') || path.endsWith('.js') || path.endsWith('.css') || path.endsWith('.svg') || path.endsWith('.webp')) {
            // 缓存一个月且缓存期间不验证（需保证所有文件都具备hash值）
            res.setHeader('Cache-Control', 'public, max-age=2592000, immutable')
        }
    },
}));

const start = async () => {
    app.listen(3000);
}

start();

console.log('Server is running on http://localhost:3000');
```
## 服务端组件支持
```
src/pages/server 写在这里
通过HOC的方式加入客户端功能
```
## PWA
```
// 在public目录下创建 sw.js文件
```
```
npm install vite-plugin-pwa -D
```
```
import { VitePWA } from 'vite-plugin-pwa'

export default {
  plugins: [
    VitePWA({
      registerType: 'autoUpdate', // 自动更新SW
      manifest: {
        name: '你的应用名',
        short_name: '应用简称',
        start_url: '.',
        display: 'standalone',
        background_color: '#ffffff',
        theme_color: '#42b983',
        icons: [
          {
            src: 'icon-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icon-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      },
      workbox: {
        // 不预缓存任何文件
        globPatterns: [],
        // 不做任何运行时缓存
        runtimeCaching: []
      },
      // 也可以用 injectRegister: 'auto'，更简单
    })
  ]
}
```

## 资源使用问题
图像资源仅推荐使用webp和svg格式 并且经过压缩处理 最好能够提供1x 2x图像
