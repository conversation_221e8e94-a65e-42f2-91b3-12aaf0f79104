import Koa from 'koa';
import serve from 'koa-static';
import Router from 'koa-router';
import send from 'koa-send';
import bodyParser from 'koa-bodyparser';
import * as cheerio from 'cheerio';
import fs from 'fs/promises';
import path from 'path';
import React from 'react';
import { renderToString } from 'react-dom/server';
import Blog from './lib/blog.es.js';

// 获取文件 MIME 类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase()
    const mimeTypes = {
        '.js': 'application/javascript',
        '.css': 'text/css',
        '.svg': 'image/svg+xml',
        // 待定
        // '.json': 'application/json',
        // '.ico': 'image/x-icon',
        // '.woff': 'font/woff',
        // '.woff2': 'font/woff2',
        // '.ttf': 'font/ttf',
        // '.eot': 'application/vnd.ms-fontobject'
    }
    return mimeTypes[ext] || 'application/octet-stream'
}

const router = new Router();

// const loadBlog = async (ctx) => {
//     try {
//         const { lang } = ctx.params;
//         let template = await fs.readFile(`./static/blog${lang ? `/${lang}/` : '/'}index.html`, 'utf-8')
//         const html = renderToString(React.createElement(Blog, {
//             posts: [],
//         }))
//         // 加载 HTML
//         const $ = cheerio.load(template)
//         // 替换 id="app" 的标签内容
//         $('#root').html(html)
//         const newHtml = $.html()
//         ctx.body = newHtml
//     } catch (error) {
//         console.log(error);
//         ctx.status = 500;
//         ctx.body = {
//             code: 500,
//             data: {},
//             message: 'Internal server error',
//         };
//     }
// }
// router.get('/blog/:lang/', loadBlog);
// router.get('/blog/', loadBlog);

const app = new Koa();

// /xxx /xxx/index.html 归因到 /xxx/
app.use(async (ctx, next) => {
    // 匹配任意路径下的 index.html
    const match = ctx.path.match(/^(.*)\/index\.html$/);
    if (match) {
        // 如果是根目录 /index.html，重定向到 /
        // 其他如 /blog/index.html 重定向到 /blog/
        const redirectPath = match[1] === '' ? '/' : match[1] + '/';
        ctx.status = 301; // 永久重定向
        ctx.redirect(redirectPath);
        return;
    }
    await next();
});

// 添加尾斜杠重定向逻辑
app.use(async (ctx, next) => {
    // 检查路径是否不以斜杠结尾且不包含文件扩展名
    if (!ctx.path.endsWith('/') && !ctx.path.includes('.') && ctx.path !== '/') {
        // 永久重定向到带尾斜杠的路径
        ctx.status = 301;
        ctx.redirect(ctx.path + '/');
        return;
    }
    await next();
});

app.use(bodyParser());

app.use(router.routes());

// 1. 优先返回 gzip 文件中间件
app.use(async (ctx, next) => {
    if (ctx.method === 'GET') {
        const acceptEncoding = ctx.headers['accept-encoding'] || ''

        // 检查是否支持 gzip
        if (acceptEncoding.includes('gzip')) {
            const gzPath = ctx.path + '.gz'
            const absPath = path.join('./static', gzPath)

            // 如果存在对应的 .gz 文件
            try {
                if (await fs.stat(absPath)) {
                    // 设置正确的响应头
                    ctx.set('Content-Type', getMimeType(ctx.path))
                    ctx.set('Content-Encoding', 'gzip')
                    ctx.set('Vary', 'Accept-Encoding')

                    // 缓存一个月且缓存期间不验证（需保证所有文件都具备hash值）
                    ctx.set('Cache-Control', 'public, max-age=2592000, immutable')
                    // 发送 gzip 文件
                    await send(ctx, gzPath, {
                        root: './static',
                        maxAge: 2592000, // 一个月缓存
                        immutable: true,
                    })
                    return
                }
            }
            catch {
                // 如果发送失败，继续下一个中间件
            }
        }
    }
    await next()
});

app.use(serve('./static', {
    // maxage: 86400 * 1000,
    setHeaders: (res, path, stats) => {
        if (path.endsWith('.gz') || path.endsWith('.js') || path.endsWith('.css') || path.endsWith('.svg') || path.endsWith('.webp')) {
            // 缓存一个月且缓存期间不验证（需保证所有文件都具备hash值）
            res.setHeader('Cache-Control', 'public, max-age=2592000, immutable')
        }
    },
}));

const start = async () => {
    app.listen(3000);
}

start();

console.log('Server is running on http://localhost:3000');
